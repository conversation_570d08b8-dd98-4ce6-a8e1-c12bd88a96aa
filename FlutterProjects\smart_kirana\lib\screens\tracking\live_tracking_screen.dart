import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/delivery_tracking_model.dart';
import '../../models/order_model.dart';
import '../../services/real_time_tracking_service.dart';
import '../../widgets/maps/enhanced_order_tracking_map.dart';
import '../../widgets/maps/live_tracking_panel.dart';
import '../../utils/constants.dart';

class LiveTrackingScreen extends StatefulWidget {
  final OrderModel order;

  const LiveTrackingScreen({super.key, required this.order});

  @override
  State<LiveTrackingScreen> createState() => _LiveTrackingScreenState();
}

class _LiveTrackingScreenState extends State<LiveTrackingScreen>
    with TickerProviderStateMixin {
  final RealTimeTrackingService _trackingService = RealTimeTrackingService();
  StreamSubscription<DeliveryTrackingModel?>? _trackingSubscription;
  DeliveryTrackingModel? _currentTracking;

  bool _isLoading = true;
  String? _error;
  bool _isPanelExpanded = false;

  late AnimationController _fabController;
  late AnimationController _panelController;
  late Animation<double> _fabAnimation;
  late Animation<double> _panelAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeTracking();
    _setSystemUIOverlay();
  }

  @override
  void dispose() {
    _trackingSubscription?.cancel();
    _fabController.dispose();
    _panelController.dispose();
    _resetSystemUIOverlay();
    super.dispose();
  }

  void _initializeAnimations() {
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _panelController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabController, curve: Curves.easeOutBack),
    );

    _panelAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _panelController, curve: Curves.easeOutCubic),
    );
  }

  void _setSystemUIOverlay() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  void _resetSystemUIOverlay() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  Future<void> _initializeTracking() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final initialized = await _trackingService.initialize();
      if (!initialized) {
        setState(() {
          _error = 'Failed to initialize tracking service';
          _isLoading = false;
        });
        return;
      }

      _startTrackingUpdates();
    } catch (e) {
      setState(() {
        _error = 'Error initializing tracking: $e';
        _isLoading = false;
      });
    }
  }

  void _startTrackingUpdates() {
    _trackingSubscription = _trackingService
        .getTrackingStream(widget.order.id)
        .listen(
          (tracking) {
            setState(() {
              _currentTracking = tracking;
              _isLoading = false;
            });

            if (tracking != null) {
              _fabController.forward();

              // Auto-expand panel for important status updates
              if (tracking.status == DeliveryStatus.nearDestination ||
                  tracking.status == DeliveryStatus.delivered) {
                _expandPanel();
              }
            }
          },
          onError: (error) {
            setState(() {
              _error = 'Error loading tracking data: $error';
              _isLoading = false;
            });
          },
        );
  }

  void _togglePanel() {
    setState(() {
      _isPanelExpanded = !_isPanelExpanded;
    });

    if (_isPanelExpanded) {
      _panelController.forward();
    } else {
      _panelController.reverse();
    }
  }

  void _expandPanel() {
    if (!_isPanelExpanded) {
      setState(() {
        _isPanelExpanded = true;
      });
      _panelController.forward();
    }
  }

  void _startDemoTracking() async {
    if (_currentTracking == null) return;

    // Start simulated movement for demo
    _trackingService
        .simulateDeliveryMovement(
          trackingId: _currentTracking!.id,
          startLocation: _currentTracking!.storeLocation,
          endLocation: _currentTracking!.destinationLocation,
          interval: const Duration(seconds: 3),
        )
        .listen((_) {
          // Movement updates are handled by the stream
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          // Map background
          _buildMapView(),

          // Loading overlay
          if (_isLoading) _buildLoadingOverlay(),

          // Error overlay
          if (_error != null) _buildErrorOverlay(),

          // Tracking panel
          if (_currentTracking != null) _buildTrackingPanel(),

          // Floating action buttons
          if (_currentTracking != null) _buildFloatingActions(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      title: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppPadding.medium,
          vertical: AppPadding.small,
        ),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(AppBorderRadius.large),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'Order #${widget.order.id.substring(0, 8)}',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
      ),
      centerTitle: true,
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black),
            onPressed: _initializeTracking,
          ),
        ),
      ],
    );
  }

  Widget _buildMapView() {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: EnhancedOrderTrackingMap(
        order: widget.order,
        height: double.infinity,
        showFullScreen: true,
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.white.withOpacity(0.8),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppPadding.medium),
            Text(
              'Loading tracking information...',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms);
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: Colors.white.withOpacity(0.9),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppPadding.large),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: AppColors.error),
              const SizedBox(height: AppPadding.medium),
              Text(
                'Tracking Unavailable',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppPadding.small),
              Text(
                _error!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppPadding.large),
              ElevatedButton(
                onPressed: _initializeTracking,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppPadding.large,
                    vertical: AppPadding.medium,
                  ),
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 300.ms);
  }

  Widget _buildTrackingPanel() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: GestureDetector(
        onTap: _togglePanel,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutCubic,
          height:
              _isPanelExpanded ? MediaQuery.of(context).size.height * 0.6 : 200,
          child: LiveTrackingPanel(
            tracking: _currentTracking!,
            onCallDeliveryAgent: () {
              // Call functionality is handled in the panel
            },
            onShareLocation: () {
              // Implement share location functionality
              _shareLocation();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActions() {
    return Positioned(
      right: AppPadding.medium,
      bottom:
          _isPanelExpanded
              ? MediaQuery.of(context).size.height * 0.6 + 20
              : 220,
      child: Column(
        children: [
          // Demo tracking button
          AnimatedBuilder(
            animation: _fabAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _fabAnimation.value,
                child: FloatingActionButton(
                  heroTag: 'demo',
                  onPressed: _startDemoTracking,
                  backgroundColor: AppColors.secondary,
                  child: const Icon(Icons.play_arrow, color: Colors.white),
                ),
              );
            },
          ),
          const SizedBox(height: AppPadding.small),

          // Center on delivery agent button
          AnimatedBuilder(
            animation: _fabAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _fabAnimation.value,
                child: FloatingActionButton(
                  heroTag: 'center',
                  onPressed: () {
                    // Center map on delivery agent
                  },
                  backgroundColor: AppColors.primary,
                  child: const Icon(Icons.my_location, color: Colors.white),
                ),
              );
            },
          ),
        ],
      ),
    ).animate().slideX(
      begin: 1.0,
      end: 0.0,
      duration: 500.ms,
      delay: 300.ms,
      curve: Curves.easeOutBack,
    );
  }

  void _shareLocation() {
    // Implement share location functionality
    // This could open a share dialog with the tracking link
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Location shared successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
