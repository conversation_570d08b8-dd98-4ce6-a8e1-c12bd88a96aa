* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Exo 2", sans-serif;
    background-color: #f0f0f0;
    color: #333;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #333;
    position: sticky;
    top: 0;
    z-index: 100;
}

nav .logo {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav a {
    text-decoration: none;
    color: #fff;
    font-size: 18px;
}

.qaza-section {
    padding: 20px 0;
    background-color: #fff;
    color: #333;
    text-align: center;
}

.qaza-section h2 {
    font-size: 36px;
    margin-bottom: 40px;
    color: #333;
}

.qaza-content {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    gap: 50px;
}

#qazaForm {
    max-width: 40vw;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#qazaForm input,
#qazaForm select {
    margin-bottom: 5px;
    width: 100%;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

#qazaForm button {
    padding: 15px;
    background-color: #ff6f61;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s ease;
}

#qazaForm button:hover {
    background-color: #e65c50;
}

.qaza-result h3 {
    font-size: 24px;
    margin-bottom: 20px;
}

.warning {
    color: red;
    margin-top: 30px;
    max-width: 35vw;
}

/* Responsive Design */
@media (max-width: 320px) {
    nav {
        padding: 2px;
    }

    .hero-content img {
        margin-top: 10vh;
        height: auto;
        width: 80px;
        border-radius: 50%;
    }
    .hero h1 {
        font-size: 18px;
        margin-bottom: 2px;
    }
    .hero p {
        font-size: 9px;
        margin-bottom: 5px;
    }
    .cta-buttons .btn {
        text-decoration: none;
        padding: 2px 2px;
        border-radius: 5px;
        font-size: 10px;
}
    .skills {
        width: 70%;
    }
    skill {
        width: 100px;
    }
    .skill-list {
        width: 300px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0 10px;
    }
}

@media (max-width: 600px) {
    .hero {
        height: 22em;
    }
    nav {
        padding: 5px;
    }

    .hero-content img {
        margin-top: 0;
        height: auto;
        width: 30%;
        border-radius: 50%;
    }
    .hero h1 {
        font-size: 22px;
        margin-bottom: 5px;
    }
    .hero p {
        font-size: 12px;
        margin-bottom: 10px;
    }
    .cta-buttons .btn {
        text-decoration: none;
        padding: 3px 5px;
        border-radius: 5px;
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .hero h1 {
        font-size: 36px;
    }

    .hero p {
        font-size: 20px;
    }

    nav ul {
        display: none;
    }
    
    .QazaBtn {
        margin-top : 40px;
    }
}