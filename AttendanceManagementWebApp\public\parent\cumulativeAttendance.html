<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cumulative Attendance Report - AMU Attendance Portal</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
</head>
<body>
    <div class="Dashboard-container">
        <!-- Navigation Bar -->
        <nav class="navbar" style="position: absolute;">
            <div class="navbar-container">
                <a href="./parentDashboard.html"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>
                <div class="dropdown">
                    <button class="dropbtn">
                        <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image">
                        Student Name &#9662;
                    </button>
                    <div class="dropdown-content">
                        <a href="./profile.html"><i class="fa-regular fa-address-card fa-fade"></i> Profile</a>
                        <a href="#" onclick="logoutParent()"><i class="fa-solid fa-arrow-right-from-bracket fa-fade"></i> <i class="fa-solid fa-person-walking fa-fade"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section with Image Slideshow -->
        <section class="hero-section">
            <div class="slideshow-container">
                <div class="slide fade">
                    <img src="../assets/amu1.jpg" class="slide-image" alt="AMU Image 1">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu2.jpeg" class="slide-image" alt="AMU Image 2">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu3.png" class="slide-image" alt="AMU Image 3">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu4.jpg" class="slide-image" alt="AMU Image 4">
                </div>
            </div>
        </section>

        <!-- Cumulative Attendance Card Section -->
        <section class="cards-section">
            <div class="card" style="width: 75%;">
                <h3>Cumulative Attendance Report</h3>
                <table id="attendance-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Course Name</th>
                            <th>Classes Held</th>
                            <th>Present</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody id="attendance-data">
                        <!-- Rows will be dynamically generated here -->
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4"><strong>Cumulative Attendance Percentage</strong></td>
                            <td id="cumulative-percentage">--</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </section>

        <footer>
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>
    <script src="../script/slideshow.js"></script>
    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script>
        // Logout function
        async function logoutParent() {
            try {
                const response = await fetch("/studentLogout", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ logout: true }),
                });
                if (response.ok) {
                    localStorage.removeItem("studentName");
                    window.location.href = "./parentLogin.html";
                } else {
                    throw new Error("Logout failed");
                }
            } catch (error) {
                console.error("Error logging out parent:", error);
            }
        }

        // Fetch the student name from database and display it in the navbar
        document.addEventListener("DOMContentLoaded", async () => {
            const response = await fetch("/api/student-profile", {
                    method: "GET",
                    credentials: "same-origin", // Ensures cookies are included in the request
                    headers: { "Content-Type": "application/json" }
                });
                student = await response.json();
                studentName = student.name;

            document.querySelector(".dropbtn").innerHTML = `
                <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image"> 
                ${studentName} &#9662;
            `;
        });

        // Fetch student attendance data from API and populate the table
        document.addEventListener("DOMContentLoaded", async () => {
            try {
                const response = await fetch(`/api/cumulative-attendance`,{
                    method: "GET",
                    credentials: "same-origin", // Ensures cookies are included in the request
                    headers: { "Content-Type": "application/json" }
                });
                const data = await response.json();

                if (response.ok) {
                    const tbody = document.getElementById("attendance-data");
                    let totalClassesHeld = 0;
                    let totalPresent = 0;

                    data.attendanceData.forEach((item) => {
                        const row = document.createElement("tr");
                        row.innerHTML = `
                            <td>${item.sNo}</td>
                            <td>${item.subName}</td>
                            <td>${item.classesHeld}</td>
                            <td>${item.present}</td>
                            <td>${item.percentage}%</td>
                        `;
                        tbody.appendChild(row);
                        totalClassesHeld += item.classesHeld;
                        totalPresent += item.present;
                    });

                    const cumulativePercentage = ((totalPresent / totalClassesHeld) * 100).toFixed(2);
                    document.getElementById("cumulative-percentage").textContent = `${cumulativePercentage}%`;
                } else {
                    console.error("Failed to fetch attendance data:", data.error);
                }
            } catch (error) {
                console.error("Error fetching attendance data:", error);
            }
        });
    </script>
</body>
</html>
