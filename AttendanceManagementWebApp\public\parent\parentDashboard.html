<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Link CSS  -->
    <link rel="stylesheet" href="../styles.css">

    <!-- Link Favicon  -->
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
    
    <title>Parent Dashboard - AMU Attendance Portal</title>
</head>
<body>
    <div class="Dashboard-container">
        <!-- Navigation Bar -->
        <nav class="navbar" style="position: absolute;">
            <div class="navbar-container">
                <a href="./parentDashboard.html"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>
                <!-- Dropdown Menu -->
                <div class="dropdown">
                    <button class="dropbtn">
                        <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image"> 
                        Student Name &#9662;
                    </button>
                    <div class="dropdown-content">
                        <a href="./profile.html"><i class="fa-regular fa-address-card fa-fade"></i> Profile</a>
                        <a href="#" onclick="logoutParent()"><i class="fa-solid fa-arrow-right-from-bracket fa-fade"></i> <i class="fa-solid fa-person-walking fa-fade"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section with Image Slideshow -->
        <section class="hero-section">
            <div class="slideshow-container">
                <div class="slide fade">
                    <img src="../assets/amu1.jpg" class="slide-image" alt="AMU Image 1">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu2.jpeg" class="slide-image" alt="AMU Image 2">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu3.png" class="slide-image" alt="AMU Image 3">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu4.jpg" class="slide-image" alt="AMU Image 4">
                </div>
            </div>
        </section>

        <!-- Cards Section -->
        <section class="cards-section">
            <!-- Academic Calendar Card -->
            <div class="card">
                <h3>Academic Calendar</h3>
                <p><a href="../assets/academic-calender2024-25.pdf" download><i class="fa-solid fa-file-arrow-down fa-shake"></i> Click to download Academic Calendar 2024-25</a></p>
            </div>
            
            <!-- Attendance Card  -->
            <div class="card">
                <h3>Attendance</h3>
                <p>Cumulative Attendance: <span id="cumulative-percentage">Loading...</span>%<br>
                <a href="./cumulativeAttendance.html"><i class="fa-solid fa-table fa-fade"></i> View cumulative attendance report</a></p>
            </div>
            <!-- Result Card -->
            <div class="card">
                <h3>Result</h3>
                <p><a href="https://ccae.amucontrollerexams.com/result_display/loginmodalresultdisplaybyfacno.php" target="_blank"><i class="fa-solid fa-square-poll-vertical fa-fade"></i> Click for examination result</a></p>
            </div>
        </section>

        <footer>
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="../script/slideshow.js"></script>
    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script>
        // Logout function
        async function logoutParent() {
            try {
                const response = await fetch("/studentLogout", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" }
                });

                if (response.ok) {
                    // Redirect to the login page after successful logout
                    window.location.href = "./parentLogin.html";
                } else {
                    console.error("Failed to logout.");
                    alert("Logout failed. Please try again.");
                }
            } catch (error) {
                console.error("Error during logout:", error);
                alert("An error occurred during logout.");
            }
        }

        // Fetch the student name from database and display it in the navbar
        document.addEventListener("DOMContentLoaded", async () => {
            const response = await fetch("/api/student-profile", {
                    method: "GET",
                    credentials: "same-origin", // Ensures cookies are included in the request
                    headers: { "Content-Type": "application/json" }
                });
                student = await response.json();
                studentName = student.name;

            document.querySelector(".dropbtn").innerHTML = `
                <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image"> 
                ${studentName} &#9662;
            `;
        });
        
        // Fetch attendance data using AJAX
        function fetchCumulativePercentage() {
            fetch('/api/cumulative-attendance', {
                method: 'GET',
                credentials: 'same-origin', // Ensures cookies are included in the request
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                let totalClassesHeld = 0;
                let totalPresent = 0;

                data.attendanceData.forEach((item) => {
                    totalClassesHeld += item.classesHeld;
                    totalPresent += item.present;
                });

                // Calculate and display cumulative percentage
                const cumulativePercentage = ((totalPresent / totalClassesHeld) * 100).toFixed(2);
                document.getElementById("cumulative-percentage").textContent = `${cumulativePercentage}`;
            })
            .catch(error => {
                console.error('Error fetching attendance data:', error);
                document.getElementById("cumulative-percentage").textContent = 'Error';
            });
        }
        // Call the function to fetch data when the page loads
        window.onload = fetchCumulativePercentage;
    </script>
</body>
</html>
