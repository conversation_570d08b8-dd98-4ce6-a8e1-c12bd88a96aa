import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/delivery_tracking_model.dart';
import '../services/real_time_tracking_service.dart';

class TrackingProvider with ChangeNotifier {
  final RealTimeTrackingService _trackingService = RealTimeTrackingService();

  DeliveryTrackingModel? _currentTracking;
  bool _isLoading = false;
  String? _error;
  StreamSubscription<DeliveryTrackingModel?>? _trackingSubscription;

  // Getters
  DeliveryTrackingModel? get currentTracking => _currentTracking;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize tracking service
  Future<bool> initialize() async {
    return await _trackingService.initialize();
  }

  // Start tracking for an order
  Future<void> startTracking(String orderId) async {
    try {
      _setLoading(true);
      _error = null;

      // Cancel any existing subscription
      await _trackingSubscription?.cancel();

      // Start listening to tracking updates
      _trackingSubscription = _trackingService
          .getTrackingStream(orderId)
          .listen(
            (tracking) {
              _currentTracking = tracking;
              _setLoading(false);
              notifyListeners();
            },
            onError: (error) {
              _setError('Error loading tracking data: $error');
              _setLoading(false);
            },
          );
    } catch (e) {
      _setError('Failed to start tracking: $e');
      _setLoading(false);
    }
  }

  // Create initial tracking for an order
  Future<String?> createTracking({
    required String orderId,
    required String deliveryAgentName,
    required String deliveryAgentPhone,
    required double destinationLatitude,
    required double destinationLongitude,
    String deliveryAgentAvatar = '',
    String vehicleType = 'Bike',
    String vehicleNumber = '',
  }) async {
    try {
      _setLoading(true);
      _error = null;

      final trackingId = await _trackingService.createDeliveryTracking(
        orderId: orderId,
        deliveryAgentId: 'demo_agent_${DateTime.now().millisecondsSinceEpoch}',
        deliveryAgentName: deliveryAgentName,
        deliveryAgentPhone: deliveryAgentPhone,
        destinationLocation: LatLng(destinationLatitude, destinationLongitude),
        deliveryAgentAvatar: deliveryAgentAvatar,
        vehicleType: vehicleType,
        vehicleNumber: vehicleNumber,
      );

      _setLoading(false);
      return trackingId;
    } catch (e) {
      _setError('Failed to create tracking: $e');
      _setLoading(false);
      return null;
    }
  }

  // Update delivery agent location
  Future<bool> updateAgentLocation({
    required String trackingId,
    required double latitude,
    required double longitude,
    DeliveryStatus? newStatus,
    String? eventMessage,
  }) async {
    try {
      return await _trackingService.updateDeliveryAgentLocation(
        trackingId: trackingId,
        newLocation: LatLng(latitude, longitude),
        newStatus: newStatus,
        eventMessage: eventMessage,
      );
    } catch (e) {
      _setError('Failed to update location: $e');
      return false;
    }
  }

  // Start demo simulation
  void startDemoSimulation({
    required String trackingId,
    required double startLat,
    required double startLng,
    required double endLat,
    required double endLng,
  }) {
    _trackingService
        .simulateDeliveryMovement(
          trackingId: trackingId,
          startLocation: LatLng(startLat, startLng),
          endLocation: LatLng(endLat, endLng),
          interval: const Duration(seconds: 3),
        )
        .listen(
          (location) {
            // Location updates are handled by the stream
            debugPrint(
              'Demo location updated: ${location.latitude}, ${location.longitude}',
            );
          },
          onError: (error) {
            _setError('Demo simulation error: $error');
          },
        );
  }

  // Complete delivery
  Future<bool> completeDelivery(String trackingId) async {
    try {
      return await _trackingService.completeDelivery(trackingId);
    } catch (e) {
      _setError('Failed to complete delivery: $e');
      return false;
    }
  }

  // Cancel delivery
  Future<bool> cancelDelivery(String trackingId, String reason) async {
    try {
      return await _trackingService.cancelDelivery(trackingId, reason);
    } catch (e) {
      _setError('Failed to cancel delivery: $e');
      return false;
    }
  }

  // Stop tracking
  void stopTracking() {
    _trackingSubscription?.cancel();
    _trackingSubscription = null;
    _currentTracking = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    debugPrint('TrackingProvider Error: $error');
    notifyListeners();
  }

  @override
  void dispose() {
    _trackingSubscription?.cancel();
    super.dispose();
  }
}
