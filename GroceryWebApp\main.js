// Project Structure for Grocery Shop Web App

// 1. Backend (Node.js + Express.js)
// 2. Frontend (React.js)
// 3. Database (MongoDB)

// Directory Structure:
// grocery-shop/
// ├── backend/
// │   ├── server.js  // Main server file
// │   ├── routes/    // API route handlers
// │   ├── models/    // Mongoose schemas
// │   ├── controllers/ // Business logic
// │   └── config/    // Configuration files (e.g., database connection)
// ├── frontend/
// │   ├── public/    // Static files (index.html, favicon, etc.)
// │   ├── src/
// │   │   ├── components/  // React components
// │   │   │   ├── ProductCard.jsx // Individual product card
// │   │   │   ├── Navbar.jsx      // Navigation bar
// │   │   │   ├── Cart.jsx        // Shopping cart
// │   │   │   └── Footer.jsx      // Footer component
// │   │   ├── pages/       // Different pages (Home, Cart, Checkout)
// │   │   │   ├── Home.jsx       // Homepage
// │   │   │   ├── Checkout.jsx   // Checkout page
// │   │   │   └── NotFound.jsx   // 404 page
// │   │   ├── services/    // API calls
// │   │   │   └── api.js         // API service file
// │   │   ├── App.js       // Main React app file
// │   │   └── index.js     // Entry point
// └── README.md           // Documentation

// Example code snippets for setting up:

// ========== BACKEND (server.js) ==========
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
mongoose.connect('mongodb://localhost:27017/grocery-shop', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
}).then(() => console.log('MongoDB Connected')).catch((err) => console.error(err));

// Example Route
app.get('/', (req, res) => {
    res.send('Welcome to Grocery Shop Backend');
});

// Start Server
const PORT = 5000;
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});

// ========== FRONTEND (React Components) ==========

// ProductCard.jsx
import React from 'react';
import './ProductCard.css';

const ProductCard = ({ product }) => {
    return (
        <div className="product-card">
            <img src={product.image} alt={product.name} className="product-image" />
            <h3>{product.name}</h3>
            <p>Price: ₹{product.price}</p>
            <button>Add to Cart</button>
        </div>
    );
};

export default ProductCard;

// Navbar.jsx
import React from 'react';
import './Navbar.css';

const Navbar = () => {
    return (
        <nav className="navbar">
            <h1>Grocery Shop</h1>
            <ul className="nav-links">
                <li>Home</li>
                <li>Cart</li>
                <li>Contact</li>
            </ul>
        </nav>
    );
};

export default Navbar;

// App.js
import React from 'react';
import Navbar from './components/Navbar';
import ProductCard from './components/ProductCard';

const App = () => {
    const sampleProduct = {
        name: 'Rice',
        price: 50,
        image: 'https://via.placeholder.com/150',
    };

    return (
        <div className="App">
            <Navbar />
            <div className="product-list">
                <ProductCard product={sampleProduct} />
            </div>
        </div>
    );
};

export default App;

// index.js
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import './index.css';

ReactDOM.render(
    <React.StrictMode>
        <App />
    </React.StrictMode>,
    document.getElementById('root')
);

// ========== DATABASE (Product Model) ==========
const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
    name: { type: String, required: true },
    price: { type: Number, required: true },
    image: { type: String },
    category: { type: String },
    stock: { type: Number, default: 0 },
});

module.exports = mongoose.model('Product', ProductSchema);
