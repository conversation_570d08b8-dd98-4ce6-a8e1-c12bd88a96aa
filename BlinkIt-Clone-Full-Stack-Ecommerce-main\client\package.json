{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.2.7", "@stripe/stripe-js": "^4.6.0", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.0", "react-type-animation": "^3.2.0", "sweetalert2": "^11.14.0"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.8.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.9", "vite": "^5.4.0"}}