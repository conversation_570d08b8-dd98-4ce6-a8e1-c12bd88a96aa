Project: Smart Kirana - Digitizing the Provisional Store with AI

Overview:
smart_kirana is a Flutter app powered by Firebase, designed to enhance the provisional shopping experience with AI-driven personalized order recommendations.

User Features:
- Login via email or phone with password (Firebase Authentication)
- Email verification via Gmail (<EMAIL>, secure passkey handling)
- Password reset functionality via email reset link (Firebase email reset)
- Product browsing by category and search
- Cart management with add/update/remove features
- Order placement with map-based tracking (through google maps platform integration i.e. Route Optimization API)
- Address management using location services
- User profile management
- View order history and details

Admin Features:
- Analytics dashboard
- Product/category management (add, edit, delete)
- Order management with status updates
- User management (CRUD)
- Invoice generation

Implementation Details:
1. Firebase Integration:
   - Firebase Auth (email/password, phone login)
   - Firestore (users, products, orders, categories)
   - Firebase security rules

2. Authentication System:
   - Email/password login and registration
   - One-time email verification on registration
   - Admin login with role-based access control

3. Product Management:
   - Product model: name, description, price, discount, category, stock
   - Product listing by category
   - Product filtering and sorting
   - Product recommendations based on user history (AI integration)
   - Product out of stock handling, Product less in stock handling
   - Product search and detail view

4. Shopping Cart:
   - Cart persistence in Firestore
   - Cart summary on main screens
   - Checkout flow integration

5. Order Processing:
   - COD and simulated online payment
   - Map-based tracking
   - Order history and detailed view

6. Admin Panel:
   - Dashboard with analytics (users, revenue, orders)
   - Full CRUD for products, categories, orders, and users
   - Invoice generation

7. Location Services:
   - Detect user location
   - Manage multiple addresses
   - Real-time order tracking with delivery agent's live location

Firestore Collections:
- users: id, name, email, phone, addresses[], isVerified, createdAt, lastLogin, role (CUSTOMER/ADMIN)
- products: id, name, description, price, discountPrice, imageUrl, categoryId, stock, unit, isPopular, isFeatured
- categories: id, name, imageUrl
- orders: id, userId, items[], totalAmount, orderDate, status, deliveryAddress, paymentMethod, deliveryNotes, estimatedDeliveryTime, subtotal, deliveryFee, discount, userName, deliveryLatitude, deliveryLongitude, currentLatitude, currentLongitude, deliveryAgentName, deliveryAgentPhone
- admins: id (same as user id), role

Default Admin:
- Email: <EMAIL>
- Password: yusuf11
- Role: ADMIN

UI Color Theme:
- Primary: #6C9A8B (calm green)
- Secondary: #F4A259 (earthy orange)
- Accent: #DDA15E (natural gold)
- Background: #F1F1E8 (light olive)
- Surface: #FFFFFF (white)
- Text Primary: #2D2D2D
- Text Secondary: #6B6B6B
- Success: #A3B18A
- Error: #E63946

Special Features:
- Login through Phone Number with Firebase Auth
- Forgot Password with Firebase email reset
- Order Tracking with map, agent location, and estimated delivery time
- Admin Dashboard with charts, recent orders, user/order/revenue metrics
- Cart Persistence using Firestore and user sessions

Note: All financial values must be handled in Indian Rupees (INR).
Note: Sensitive information must store in .env file. (The .env file is located at FlutterProjects/smart_kirana/.env in the user's workspace.)