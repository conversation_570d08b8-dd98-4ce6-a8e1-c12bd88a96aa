#include<iostream>
using namespace std;
int main(){
    int n;
    cout<<"Enter the number of elements:";
    cin>>n;

    int arr[n];
    cout<<"Enter the elements:";

    // enter elements

    for(int i=0; i<n; i++){
        cin>>arr[i];
    }

    // print the original array
    for(int i=0; i<n; i++){
        cout<<arr[i]<<" ";
    }

    cout<<endl;

    // push zeros to end
    for(int i=0; i<n-1; i++){
        if(arr[i] == 0){
            for(int j = i+1; j<n; j++){
                if(arr[j] > 0){
                    swap(arr[i], arr[j]);
                    break;
                }
            }
        }
    }

    // print the updated array
    for(int i=0; i<n; i++){
        cout<<arr[i]<<" ";
    }
}