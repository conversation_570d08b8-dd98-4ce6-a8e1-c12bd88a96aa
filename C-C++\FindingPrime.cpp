#include<iostream>
using namespace std;
int main()
{
    // int n;
    // cout<<"Enter a number that you want to check :";
    // cin>>n;
    // if (n<1) cout<<"Ah Ah!! Invalid Input";
    // else if(n==1) cout<<1<<" is neither Prime nor Composite.";
    // else if(n==2) cout<<2<<" is smallest Prime number that is only even Prime.";
    // else
    // {
        for(int j=2; j>1; j++)
        {
            bool a=true;
            for(int i=2; i<=j/2; i++)
            {
                if(j%i==0) a=false;
            }
            // if (a==true) cout<<"Your number is Prime";
            // if(a!=true) cout<<"Your number is composite";
            if (a==true) cout<<j<<" , ";
        }
    // }

    return 0;
}
