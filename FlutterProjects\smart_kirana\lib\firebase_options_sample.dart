// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    appId: '1:156xxx512xx15:web:dxxxxxxxxxxxxxx4xxx',
    messagingSenderId: 'xxx46xx515x',
    projectId: 'sxxxt-kxxxa-8xx19',
    authDomain: 'sxxxt-kxxxa-8xx19.firebaseapp.com',
    storageBucket: 'sxxxt-kxxxa-8xx19.firebasestorage.app',
    measurementId: 'G-41xxxxx54xW',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'xxxxxxxxxx454xxxxxx1545xxxxxx545xxx',
    appId: '1:56x26xxx556xx:android:xxx4545x5515x5545xx',
    messagingSenderId: 'xx6542x1551x',
    projectId: 'sxxxt-kxxxa-8xx19',
    storageBucket: 'sxxxt-kxxxa-8xx19.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'xxxxxxxxxxx654165x14456x1564x21xx',
    appId: '1:xx543x151512x:ios:xx485451x55x51x5xxx515',
    messagingSenderId: 'xx616x31x55x',
    projectId: 'sxxxt-kxxxa-8xx19',
    storageBucket: 'sxxxt-kxxxa-8xx19.firebasestorage.app',
    iosClientId:
        'xx545xxxxx2154x45-xxxdc511xx1x2151x21x51x12x1.apps.googleusercontent.com',
    iosBundleId: 'com.example.smartKirana',
  );
}
