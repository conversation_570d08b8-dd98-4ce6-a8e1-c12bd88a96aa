#include<iostream>
using namespace std;

pushZerosToEnd(int arr[], int n){
    int zeroIndex = 0;
    for(int i = 0; i < n; i++){
        if(arr[i] != 0){
            swap(arr[i], arr[zeroIndex]);
            zeroIndex++;
        }
    }
}

int main(){
    int n;
    cout<<"Enter the number of elements:";
    cin>>n;

    int arr[n];
    
    // enter elements 
    cout<<"Enter the elements:";
    for(int i = 0; i < n; i++){
        cin>>arr[i];
    }

    // Print original array 
    for(int i = 0; i < n; i++){
        cout<<arr[i]<< " ";
    }
    cout<<endl;

    // push zeros to end
    pushZerosToEnd(arr, n);

    // print the updated array 

    for(int i = 0; i < n; i++){
        cout<<arr[i]<< " ";
    }

}