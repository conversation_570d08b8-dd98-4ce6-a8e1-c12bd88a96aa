{"aadc_info": {"age_group": 0}, "account_info": [{"access_point": 17, "account_id": "00034001CE59CC55", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "c24dec80bb506ed6", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON><PERSON><PERSON>", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "<PERSON>", "edge_account_location": "IN", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "00034001CE59CC55", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "00034001CE59CC55", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_last_notification_shown": "*****************", "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136, "roaming_card_silently_enabled": true, "upload_encoding_seed": "D8717A2F847C56AFC98E0693D912C2B8"}, "bookmark_bar": {"show_on_all_tabs": true, "show_only_on_ntp": false}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "_gaming_assist_": {"order": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": **********, "523b5ef3-0b10-4154-8b62-10b2ebd00921": **********, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": -**********, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": -1073741830, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 536870911, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": -536870919, "96defd79-4015-4a32-bd09-794ff72183ef": 2147483644}}, "add_app_to_bottom": true, "order": {"523b5ef3-0b10-4154-8b62-10b2ebd00921": 1431655764, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 954437176, "d304175e-bbd4-4b66-8839-9627e56f391f": 477218588}}, "edge_sidebar_visibility_debug": {"order_list": ["Search", "Microsoft Shopping"], "order_raw_data": {"523b5ef3-0b10-4154-8b62-10b2ebd00921": {"name": "Microsoft Shopping", "pos": "1431655764"}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "954437176"}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"name": "unknown", "pos": "477218588"}}}, "edge_split_window_last_used_time": "13370424407973358", "editor_proofing_languages": {"en-IN": {"Grammar": true, "Spelling": true}, "en-US": {"Grammar": true, "Spelling": true}, "hi": {"Spelling": true}}, "enable_text_prediction_v2": true, "gamer_mode_asset_store_prefs": {"779d97ed-2254-4943-a1f3-c811fa709092": {"gamer_mode_modal_script_hash": "xie40asvhdbPXzggtqUJ4lfglpLAYbJeXpWhq51+U+s=", "gamer_mode_modal_script_url": "https://edgeassetservice.azureedge.net/assets/gamer_mode_modal_ux/1.1.69/asset?assetgroup=GamerModeModalUX"}}, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "3458dfd2-bf1b-4d00-a6dd-a74a59d523c7": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {"2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "2cb2db96-3bd0-403e-abe2-9269b3761041": {"auto_show": {"enabled": true}}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"auto_show": {"enabled": true}}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"all_scenarios": {"auto_open": {"enabled": false}}, "auto_show": {"enabled": false}}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"auto_show": {"enabled": true}, "last_path": ""}, "b7a8e9f2-6b0d-4c5b-ae7d-8a6e1f2c7a6f": {"all_scenarios": {"auto_open": {"enabled": true}}, "c8ebbf64-e3b8-41d9-925f-4e3754151ff7": {"auto_show": {"enabled": true}}}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"auto_show": {"enabled": true}}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"auto_show": {"enabled": true}}, "default_on_apps_cleanup_state": 1}, "hub_app_usage_preferences": {"529de4f7-b6c4-4c76-b36d-c511c9328ebe": 309, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 1, "CleanupCounts": 1, "OpenFirstTime": 1684001742, "cd4688a9-e888-48ea-ad81-76193d56b1be": 14}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context": {"cleanup_last_time_v3": 1723858215.724317, "show_days": "11111111110000000000111100010011", "sidebar_show_last_time": 3712141}, "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_edge_split_window_toolbar_button": false, "show_hub_app_in_sidebar_buttons": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 3, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 0, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 3, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "d304175e-bbd4-4b66-8839-9627e56f391f": 0, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3}, "show_hub_app_in_sidebar_buttons_legacy": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 3, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 0, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 3, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13392546575375670", "show_toolbar_bookmarks_button": false, "show_toolbar_collections_button": false, "show_toolbar_history_button": false, "show_toolbar_share_button": true, "show_toolbar_vertical_tabs_button": true, "time_of_last_normal_window_close": "13392547317901347", "toolbar_browser_essentials_button_pinned": false, "underside_chat_bing_signed_in_status": false, "user_level_features_context": {}, "window_placement": {"bottom": 808, "left": 8, "maximized": false, "right": 1060, "top": 8, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "browser_content_container_height": 712, "browser_content_container_width": 1038, "browser_content_container_x": 0, "browser_content_container_y": 81, "browser_essentials": {"last_used_time": "13389029567403876", "show_hub_fre": false, "show_safety_fre": false}, "collections": {"prism_collection": {"enroll": {"rule_version": 1, "state": 2}}, "prism_collections": {"enabled": 0, "migration": {"accepted": true, "completed": 2, "item_count": 0}, "policy": {"cached": 0}, "wns": {"last_subscribe_time": "13392525632251458", "subscription_id": "1;3602683959618390957"}}}, "commerce_daily_metrics_last_update_time": "13392525589063734", "copilot_vision": {"user_access": true}, "countryid_at_install": 18766, "credentials_enable_breachdetection": true, "custom_links": {"list": []}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": "", "user_list_data_1": {}}, "edge": {"account_type": 1, "auto_grouping": {"applied_suggestions_tutorial_shown": true, "seen_suggestions_in_bubble": true, "tutorial_shown": true}, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"last_gaia_id": "00034001CE59CC55", "signin_scoped_device_id": "6124a86f-986d-46c4-8439-2615f0b35272"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}", "storage": {"state": "{\"app_folder_path\":\"\",\"container_id\":\"\",\"drive_id\":\"\",\"prefs_item_id\":\"\",\"storage_endpoint\":\"\",\"version\":3}"}}}, "edge_cloud_messaging": {"cached_target_token": {"cv": "978480767778685184", "target_token": "G0vlqAXQsCXKid+Ip+b+Og==$M7/9lqsEt/Qur6QrJFjrFBbECEz9s/awRzTx1BrPqdBS8pV25jqxZcwtfBMrQ5T5f1pv5HGrGpFL0K6KRmqeSZIlUt3QlsRhy7iduHmhJ+gJGIv/p1izT5CGIr26T8U+5+FAQRlJyBxweyuMHG7/IiMqFQeyeIDTIBuxN+etyhQap1bRDQWYhs/MEX8ziBmH", "time": "13392526393568580"}}, "edge_rewards": {"cache_data": "CAEQ3BMYAEoCaW4=", "coachmark_promotions": {}, "hva_promotions": [], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "Offer valid for 1 person/account within 7 days of joining the challenge", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "Get started", "edgebar_title": "Welcome to search bar powered by Microsoft Edge! Get a free gift card when you search here for 3 days.", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "Get started", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ENIN_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13393130388799528"}, "edge_share": {"enhanced_copy_paste": {"nurturing": {"most_recent_showing_of_paste_pref_awareness_ui": "13275229209785766", "num_times_paste_pref_awareness_ui_shown": 1}}}, "edge_ux_config": {"assignmentcontext": "9+BcnIE246LGW7N6ZEr1GhtMhR+ZZOdcpKfssMaauNs=", "dataversion": "254460955", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shop-75th99": {"edgeServerUX.shopping.aablockth": 75, "edgeServerUX.shopping.block99": true}, "shopphinsightsv2-t": {"edgeServerUX.shopping.enablePhInsightsV2": true}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributionc": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": false}}, "flights": {"2f717976": "31213786", "chromeextattopcf": "31312034", "shop-75th99": "31271456", "shopphinsightsv2-t": "31303589", "shopppdismisstreatment": "31004791", "shoprevenuattributionc": "31235886"}, "latestcorrelationid": "Ref A: 2DFF6D966A374C2186B00FB8C4D54CA3 Ref B: DEL01EDGE0810 Ref C: 2025-05-24T07:47:36Z"}, "edge_wallet": {"ec_cool_down_time": "13386060163027439", "ec_dismiss_count": 3, "home": {"fre": {"autosave_step_completed": true, "completed": 0, "passwords_step_completed": true, "passwords_step_completion_state": 1}}, "passwords": {"latest_password_management_count": {"2025-05-19": 1}, "latest_password_usage_count": {"2025-05-16": 2, "2025-05-18": 4, "2025-05-21": 1}, "password_lost_report_date": "13392462669639372"}, "trigger_funnel": {"records": []}}, "enable_do_not_track": true, "enhanced_tracking_prevention": {"user_pref": 2}, "enterprise_profile_guid": "a5c6afb3-b702-4204-b489-b7c6912d6d62", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.3240.76", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "ui": {"allow_chrome_webstore": true}}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 136}, "google": {"services": {"consented_to_sync": true, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-05-24T01:59:49.307Z", "value": "17"}}}}, "history": {"thumbnail_visibility": true, "thumbnail_visibility_per_usage": true}, "import_items_failure_state": {"reimport": {"ie_react": 62432}}, "instrumentation": {"bookmark_bar": {"show_on_all_tabs": "chrome::ToggleBookmarkBarWhenVisible;true", "show_only_on_ntp": "chrome::ToggleBookmarkBarWhenVisible;false"}, "ntp": {"layout_mode": "updateLayout;3;1747615374884", "news_feed_display": "updateFeeds;off;1747615443000"}}, "intl": {"accept_languages": "en-IN,hi,en-US", "selected_languages": "en-IN,hi,en-US"}, "lightning": {"import_button_dismissed": true}, "local_browser_data_share": {"index_last_cleaned_time": "13392525649067211", "pin_recommendations_eligible": false}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "kJffWZPnbMdqCsxOMjlspE8Y2hfpns5Yppid+jXiklqW30MwB6OCGNXfiI0FGoRdh3ASQKE8AAV5d3cbw5cY1Q=="}, "muid": {"last_sync": "13392525589060666", "values_seen": ["15E5B3F8696B6132042AA60E689960D9", "23185AD985746B9B088A4F2F84726AA6", "2687E50DA4436ACF336CF0FBA5456B1D"]}, "new_device_fre": {"imported_google_data": "75", "imported_google_data_timestamp": "13380950497165547"}, "ntp": {"background_image_type": "off", "enable_widgets_region": false, "feed_engagement_time": "13392529576665243", "hide_default_top_sites": false, "layout_mode": 3, "news_feed_display": "off", "next_site_suggestions_available": false, "num_personal_suggestions": 1, "quick_links_options": 1, "record_user_choices": [{"setting": "layout_mode", "source": "ntp", "timestamp": 1747615372841.0, "value": 0}, {"setting": "daily_discovery", "source": "ntp", "timestamp": 1671373282324.0, "value": true}, {"setting": "single_column", "source": "ntp", "timestamp": 1671373090690.0, "value": false}, {"setting": "tscollapsed", "source": "updatePrefTSCollapsed", "timestamp": 1736564125728.0, "value": 0}, {"setting": "coachmarks", "source": "ntp", "timestamp": 1684834805026.0, "value": [{"coachMarkKey": "informationalSwitchOffer", "latestShownTimestamp": 1684834805026.0, "maxShowTimes": 2, "repeatHours": 168, "showTimes": 2}]}, {"setting": "seen_interest_fre_count", "source": "ntp", "timestamp": 1687767601325.0, "value": 1}, {"setting": "dismiss_scroll_down_button", "source": "ntp", "timestamp": 1700948834599.0, "value": true}, {"setting": "breaking_news_dismissed", "source": "ntp", "timestamp": 1747617699292.0, "value": {}}, {"setting": "ntp.news_feed_display", "source": "ntp", "timestamp": 1747615363934.0, "value": "off"}, {"setting": "quick_links_options", "source": "ntp", "timestamp": 1736564125728.0, "value": "onerow"}, {"setting": "ntp.topsites_open_in_new_tab", "source": "ntp", "timestamp": 1736564160396.0, "value": false}], "show_greeting": true}, "ntp_partner_code": "W046", "nurturing": {"time_of_last_sync_consent_view": "13392525589332144"}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false}, "personalization_data_consent": {"how_set": 2, "personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0, "when_set": "13392035077175782"}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {"https://drive.google.com:443,*": {"last_modified": "13378056011712299", "setting": 1}, "https://web.whatsapp.com:443,*": {"last_modified": "13365956615598887", "setting": 1}}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13392546455496982", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {"http://localhost:55850,*": {"last_modified": "13392525627745372", "last_visit": "13392086400000000", "setting": 1}, "http://localhost:63759,*": {"last_modified": "13392546696414383", "last_visit": "13392086400000000", "setting": 1}}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:55850,*": {"expiration": "13400302062406805", "last_modified": "13392526062406816", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:56658,*": {"expiration": "13400302498903284", "last_modified": "13392526498903294", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63759,*": {"expiration": "13400323317893166", "last_modified": "13392547317893188", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"http://localhost:56658,*": {"last_modified": "13392526479170475", "setting": {"Geolocation": {"dismiss_count": 1}}}, "http://localhost:63759,*": {"last_modified": "13392546486109807", "setting": {"Geolocation": {"dismiss_count": 1}}}}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:55850,*": {"last_modified": "13392526044366517", "setting": {"lastEngagementTime": 1.3392526044366488e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:56658,*": {"last_modified": "13392526484118299", "setting": {"lastEngagementTime": 1.3392526484118268e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:62749,*": {"last_modified": "13392534944944551", "setting": {"lastEngagementTime": 1.3392534944944532e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:63759,*": {"last_modified": "13392547304546303", "setting": {"lastEngagementTime": 1.3392547304546268e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {"https://maps.gstatic.com:443,*": {"last_modified": "13392546517275547", "setting": {"count": 3}}}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"geolocation": [{"action": 0, "prompt_disposition": 1, "time": "13392525627747773"}, {"action": 2, "prompt_disposition": 1, "time": "13392526479170425"}, {"action": 2, "prompt_disposition": 1, "time": "13392546486109759"}, {"action": 0, "prompt_disposition": 1, "time": "13392546696416442"}]}, "pref_version": 1}, "created_by_version": "136.0.3240.76", "creation_time": "13392525588752185", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "59ebd0dc-c96a-4bb8-82bb-e695abfc62fa", "edge_user_with_non_zero_passwords": true, "exit_type": "Normal", "hard_yes_password_monitor_consent": true, "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_engagement_time": "13392547304546269", "last_time_obsolete_http_credentials_removed": 1748052048.828754, "last_time_password_monitor_consent_shown": "13277386688386919", "last_time_password_monitor_ooc_alert_shown": "13391314987594468", "last_time_password_store_metrics_reported": 1748052018.819034, "managed_user_id": "", "name": "Profile 1", "network_pbs": {"4d67fac": {"last_updated": "13392462656012081", "pb": 13}}, "number_password_monitor_consent_shown": 3, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_136.0.3240.76": 891.0}, "one_time_permission_prompts_decided_count": 4, "password_breach_last_scanned_time": "13392035075041591", "password_breach_scan_trigger_last_reset_time": "13314479264854437", "password_breach_scan_triggered_count": 0, "password_breach_scan_triggered_password_count": 0, "password_breach_scanned": true, "password_hash_data_list": [], "signin_fre_seen_time": "13392525588781054", "using_default_avatar": false, "using_gaia_avatar": true, "were_old_google_logins_removed": true}, "read_aloud": {"last_used_time": "13381213524301019"}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13392546455299370", "extension_telemetry_file_data": {}}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"crashed": false, "time": "13392525588821487", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392526062390986", "type": 2, "window_count": 1}, {"crashed": false, "time": "13392526391323503", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13392526498889969", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "last_pwilo_api_fetch_time": "*****************", "pcb_supported": true}, "should_read_incoming_syncing_theme_prefs": false, "signin": {"accounts_metadata_dict": {"00034001CE59CC55": {"BookmarksExplicitBrowserSigninEnabled": false, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "signin_with_explicit_browser_signin_on": true, "sync_paused_start_time": "*****************"}, "smart_explore": {"on_image_hover": false}, "spellcheck": {"dictionaries": ["en-US", "hi", "en-IN"], "dictionary": ""}, "surf_game": {"buoy_highscore": 2, "classic_highscore": 1628, "speed_highscore": -1}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "cached_passphrase_type": 2, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "edge_account_type": 1, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "gaia_id": "00034001CE59CC55", "has_been_enabled": true, "has_setup_completed": true, "history_edge_supported": true, "keep_everything_synced": true, "keystore_encryption_key_state": "************************************************************************************************************************************************************************************************************************************************************************************", "local_data_out_of_sync": false, "local_device_guids_with_timestamp": [{"cache_guid": "/UtmUqYwM94GxV0VsJuJtw==", "timestamp": 155006}], "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "transport_data_per_account": {"p37/ww2Mra9L+KZX3NLoBKdYTtCxrD3XOAm10zVYs/s=": {"sync.bag_of_chips": "", "sync.birthday": "ProductionEnvironmentDefinition", "sync.cache_guid": "/UtmUqYwM94GxV0VsJuJtw==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "***********"}}, "typed_urls": true}, "sync_consent_recorded": true, "sync_profile_info": {"edge_ci_consent_last_modified_date": "*****************", "edge_ci_consent_last_shown_date": "*****************", "edge_ci_is_option_explicitly_selectedby_user": false, "edge_san_consent_last_modified_date": "*****************", "edge_san_consent_last_shown_date": "*****************", "edge_san_is_option_explicitly_selectedby_user": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "tab_groups_migration_version": 3, "third_party_search": {"consented": true}, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "tracking_prevention": {"strict_inprivate": false}, "translate_blocked_languages": ["en", "hi"], "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled": true, "personalization_data_consent_enabled_last_known_value": true}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_app_install_metrics": {"pkbaefceaphdcdegcedcaemojjcjfkmb": {"install_source": 16, "install_timestamp": "*****************"}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136", "link_handling_info": {"enabled_for_installed_apps": true}}}