// Project Structure for Grocery Shop Web App

// 1. Backend (Node.js + Express.js)
// 2. Frontend (React.js)
// 3. Database (MongoDB)

// Directory Structure:
// grocery-shop/
// ├── backend/
// │   ├── server.js  // Main server file
// │   ├── routes/    // API route handlers
// │   ├── models/    // Mongoose schemas
// │   ├── controllers/ // Business logic
// │   └── config/    // Configuration files (e.g., database connection)
// ├── frontend/
// │   ├── src/
// │   │   ├── components/  // React components
// │   │   ├── pages/       // Different pages (Home, Cart, Checkout)
// │   │   ├── services/    // API calls
// │   │   └── App.js       // Main React app file
// └── README.md           // Documentation

// Example code snippets for setting up:

// ========== BACKEND (server.js) ==========
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
mongoose.connect('mongodb://localhost:27017/grocery-shop', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
}).then(() => console.log('MongoDB Connected')).catch((err) => console.error(err));

// Example Route
app.get('/', (req, res) => {
    res.send('Welcome to Grocery Shop Backend');
});

// Start Server
const PORT = 5000;
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});

// ========== FRONTEND (React Component: ProductCard.jsx) ==========
import React from 'react';

const ProductCard = ({ product }) => {
    return (
        <div className="product-card">
            <img src={product.image} alt={product.name} />
            <h3>{product.name}</h3>
            <p>Price: ₹{product.price}</p>
            <button>Add to Cart</button>
        </div>
    );
};

export default ProductCard;

// ========== DATABASE (Product Model) ==========
const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
    name: { type: String, required: true },
    price: { type: Number, required: true },
    image: { type: String },
    category: { type: String },
    stock: { type: Number, default: 0 },
});

module.exports = mongoose.model('Product', ProductSchema);
