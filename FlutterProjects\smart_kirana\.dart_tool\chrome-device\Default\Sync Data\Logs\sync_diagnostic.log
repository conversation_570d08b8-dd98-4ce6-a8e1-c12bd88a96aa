2025-05-24 01:59:48.823: [INFO][Sync] Reset engine, reason: 8
2025-05-24 01:59:48.823: [INFO][Sync] Stopped: Bookmarks
2025-05-24 01:59:48.823: [INFO][Sync] Stopped: Preferences
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Passwords
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Autofill Profiles
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Autofill
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Extensions
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Sessions
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Extension settings
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: History Delete Directives
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Device Info
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: User Consents
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Send Tab To Self
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Web Apps
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: History
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Saved Tab Group
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Collection
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Edge E Drop
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Edge Hub App Usage
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Edge Tab Group
2025-05-24 01:59:48.824: [INFO][Sync] Stopped: Edge Wallet
2025-05-24 01:59:48.824: [INFO][Sync] SyncState after authenticated was: NotSignedIn
2025-05-24 01:59:49.067: [INFO][Sync] Reset engine, reason: 8
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Bookmarks
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Preferences
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Passwords
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Autofill Profiles
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Autofill
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Extensions
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Sessions
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Extension settings
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: History Delete Directives
2025-05-24 01:59:49.067: [INFO][Sync] Stopped: Device Info
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: User Consents
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Send Tab To Self
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Web Apps
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: History
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Saved Tab Group
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Collection
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Edge E Drop
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Edge Hub App Usage
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Edge Tab Group
2025-05-24 01:59:49.068: [INFO][Sync] Stopped: Edge Wallet
2025-05-24 01:59:49.335: [INFO][Sync] Try to start sync engine
2025-05-24 01:59:49.337: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-24 01:59:49.337: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-24 01:59:49.338: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-24 01:59:49.338: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-24 01:59:49.338: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-24 01:59:49.492: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-24 01:59:49.492: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-24 01:59:49.492: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-24 01:59:51.004: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-24 01:59:51.004: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-24 01:59:51.004: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-24 01:59:51.004: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-24 01:59:51.004: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-24 01:59:51.004: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-24 01:59:51.966: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-24 01:59:51.966: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 3, current state: 0
2025-05-24 01:59:51.966: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-05-24 01:59:51.969: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Bookmarks
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Preferences
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Extensions
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Sessions
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Extension settings
2025-05-24 01:59:51.969: [INFO][Sync] Loading: History Delete Directives
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Device Info
2025-05-24 01:59:51.969: [INFO][Sync] Loading: User Consents
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Send Tab To Self
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Web Apps
2025-05-24 01:59:51.969: [INFO][Sync] Loading: History
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Saved Tab Group
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Collection
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Edge E Drop
2025-05-24 01:59:51.969: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-24 01:59:51.970: [INFO][Sync] Loading: Edge Tab Group
2025-05-24 01:59:51.970: [INFO][Sync] Loading: Edge Wallet
2025-05-24 01:59:51.986: [INFO][Sync] All data types are ready for configure.
2025-05-24 01:59:52.955: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-05-24 01:59:52.955: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-05-24 01:59:52.960: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-24 01:59:52.960: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Bookmarks
2025-05-24 01:59:52.960: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Preferences
2025-05-24 01:59:52.960: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extensions
2025-05-24 01:59:52.960: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Sessions
2025-05-24 01:59:52.960: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extension settings
2025-05-24 01:59:52.960: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History Delete Directives
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Device Info
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Send Tab To Self
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Web Apps
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Saved Tab Group
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Collection
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge E Drop
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Hub App Usage
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Tab Group
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Wallet
2025-05-24 01:59:52.961: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 3
2025-05-24 01:59:52.961: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 01:59:52.962: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-24 01:59:52.962: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-05-24 01:59:52.962: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-05-24 01:59:52.962: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-24 01:59:52.962: [INFO][Sync] Loading: Passwords
2025-05-24 01:59:52.962: [INFO][Sync] Loading: Autofill Profiles
2025-05-24 01:59:52.962: [INFO][Sync] Loading: Autofill
2025-05-24 01:59:52.962: [INFO][Sync] All data types are ready for configure.
2025-05-24 01:59:52.962: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-24 01:59:52.962: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill Profiles
2025-05-24 01:59:52.962: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill
2025-05-24 01:59:52.962: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 01:59:52.962: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 01:59:52.962: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-24 01:59:52.963: [INFO][Sync] Prepare to configure types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-24 01:59:52.963: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys with reason: 5
2025-05-24 01:59:52.963: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-24 01:59:53.486: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-05-24 01:59:53.505: [INFO][Sync] ConfigurationDone, failed: , succeeded: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys, remaining count: 3
2025-05-24 01:59:53.505: [INFO][Sync] Prepare to configure types: Bookmarks, Encryption Keys
2025-05-24 01:59:53.505: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Bookmarks, Encryption Keys with reason: 5
2025-05-24 01:59:53.505: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Bookmarks, Encryption Keys
2025-05-24 01:59:54.090: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Bookmarks, Encryption Keys
2025-05-24 01:59:54.094: [INFO][Sync] ConfigurationDone, failed: , succeeded: Bookmarks, Encryption Keys, remaining count: 2
2025-05-24 01:59:54.095: [INFO][Sync] Prepare to configure types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-24 01:59:54.095: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys with reason: 5
2025-05-24 01:59:54.095: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-24 02:00:05.500: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys
2025-05-24 02:00:05.502: [INFO][Sync] ConfigurationDone, failed: , succeeded: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, Collection, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, remaining count: 1
2025-05-24 02:00:05.505: [INFO][Sync] Prepare to configure types: History, Encryption Keys
2025-05-24 02:00:05.505: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: History, Encryption Keys with reason: 5
2025-05-24 02:00:05.506: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: History, Encryption Keys
2025-05-24 02:00:30.001: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: History, Encryption Keys
2025-05-24 02:00:30.006: [INFO][Sync] ConfigurationDone, failed: , succeeded: History, Encryption Keys, remaining count: 0
2025-05-24 02:00:30.010: [INFO][Sync]     Configuration completed, state: 7
2025-05-24 02:00:30.011: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-24 02:00:30.017: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-24 02:07:42.641: [INFO][Sync] Reset engine, reason: 0
2025-05-24 02:07:42.641: [INFO][Sync] Reset engine with reason: 0
2025-05-24 02:13:11.325: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-05-24 02:13:12.821: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-24 02:13:12.821: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-24 02:13:12.821: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-24 02:13:13.192: [INFO][Sync] Try to start sync engine
2025-05-24 02:13:13.194: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-24 02:13:13.194: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-24 02:13:13.194: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-24 02:13:13.194: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-24 02:13:13.194: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-24 02:13:13.194: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-24 02:13:13.252: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-24 02:13:13.252: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-24 02:13:13.252: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-24 02:13:13.252: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-24 02:13:13.252: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-24 02:13:14.140: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-24 02:13:14.154: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-24 02:13:14.154: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-05-24 02:13:14.154: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-05-24 02:13:14.158: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Bookmarks
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Preferences
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Extensions
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Sessions
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Extension settings
2025-05-24 02:13:14.158: [INFO][Sync] Loading: History Delete Directives
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Device Info
2025-05-24 02:13:14.158: [INFO][Sync] Loading: User Consents
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Send Tab To Self
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Web Apps
2025-05-24 02:13:14.158: [INFO][Sync] Loading: History
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Saved Tab Group
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Collection
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Edge E Drop
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Edge Tab Group
2025-05-24 02:13:14.158: [INFO][Sync] Loading: Edge Wallet
2025-05-24 02:13:14.168: [INFO][Sync] All data types are ready for configure.
2025-05-24 02:13:14.784: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-05-24 02:13:14.784: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-05-24 02:13:14.794: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-24 02:13:14.794: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-24 02:13:14.794: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 02:13:14.795: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-24 02:13:14.795: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-05-24 02:13:14.795: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-05-24 02:13:14.795: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-24 02:13:14.795: [INFO][Sync] Loading: Passwords
2025-05-24 02:13:14.795: [INFO][Sync] Loading: Autofill Profiles
2025-05-24 02:13:14.795: [INFO][Sync] Loading: Autofill
2025-05-24 02:13:14.795: [INFO][Sync] All data types are ready for configure.
2025-05-24 02:13:14.795: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-24 02:13:14.795: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 02:13:14.795: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 02:13:14.795: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-24 02:13:14.795: [INFO][Sync] Prepare to configure types: Passwords, Encryption Keys
2025-05-24 02:13:14.795: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Passwords, Encryption Keys with reason: 5
2025-05-24 02:13:14.795: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Passwords, Encryption Keys
2025-05-24 02:13:15.160: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Passwords, Encryption Keys
2025-05-24 02:13:15.164: [INFO][Sync] ConfigurationDone, failed: , succeeded: Passwords, Encryption Keys, remaining count: 3
2025-05-24 02:13:15.164: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 02:13:15.164: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 02:13:15.164: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-05-24 02:13:15.164: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 02:13:15.164: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 02:13:15.168: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-05-24 02:13:15.171: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 02:13:15.171: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 02:13:15.172: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-05-24 02:13:15.175: [INFO][Sync]     Configuration completed, state: 7
2025-05-24 02:13:15.175: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-24 02:13:15.179: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-24 02:14:59.158: [INFO][Sync] Reset engine, reason: 0
2025-05-24 02:14:59.158: [INFO][Sync] Reset engine with reason: 0
2025-05-24 07:47:35.070: [INFO][Sync] SyncState after authenticated was: FeatureCanStart
2025-05-24 07:47:35.960: [INFO][Sync] Try to start sync engine
2025-05-24 07:47:35.962: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-05-24 07:47:35.962: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-05-24 07:47:35.962: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-05-24 07:47:35.962: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-05-24 07:47:35.962: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-05-24 07:47:36.014: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-24 07:47:36.014: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-24 07:47:36.014: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-05-24 07:47:36.481: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-05-24 07:47:36.481: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-05-24 07:47:36.481: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-05-24 07:47:36.481: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-05-24 07:47:36.481: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-05-24 07:47:36.481: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-05-24 07:47:37.137: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-05-24 07:47:37.137: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 4, current state: 0
2025-05-24 07:47:37.137: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-05-24 07:47:37.142: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Bookmarks
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Preferences
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Extensions
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Sessions
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Extension settings
2025-05-24 07:47:37.142: [INFO][Sync] Loading: History Delete Directives
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Device Info
2025-05-24 07:47:37.142: [INFO][Sync] Loading: User Consents
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Send Tab To Self
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Web Apps
2025-05-24 07:47:37.142: [INFO][Sync] Loading: History
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Saved Tab Group
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Collection
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Edge E Drop
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Edge Hub App Usage
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Edge Tab Group
2025-05-24 07:47:37.142: [INFO][Sync] Loading: Edge Wallet
2025-05-24 07:47:37.153: [INFO][Sync] All data types are ready for configure.
2025-05-24 07:47:37.635: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-05-24 07:47:37.636: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-05-24 07:47:37.645: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-05-24 07:47:37.646: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 4
2025-05-24 07:47:37.646: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 07:47:37.646: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-24 07:47:37.646: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-05-24 07:47:37.646: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-05-24 07:47:37.646: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys; Allow fail: 
2025-05-24 07:47:37.646: [INFO][Sync] Loading: Passwords
2025-05-24 07:47:37.646: [INFO][Sync] Loading: Autofill Profiles
2025-05-24 07:47:37.646: [INFO][Sync] Loading: Autofill
2025-05-24 07:47:37.646: [INFO][Sync] All data types are ready for configure.
2025-05-24 07:47:37.646: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-05-24 07:47:37.646: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 07:47:37.646: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 07:47:37.646: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-05-24 07:47:37.647: [INFO][Sync] Prepare to configure types: Passwords, Encryption Keys
2025-05-24 07:47:37.647: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Passwords, Encryption Keys with reason: 5
2025-05-24 07:47:37.647: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Passwords, Encryption Keys
2025-05-24 07:47:38.016: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Passwords, Encryption Keys
2025-05-24 07:47:38.020: [INFO][Sync] ConfigurationDone, failed: , succeeded: Passwords, Encryption Keys, remaining count: 3
2025-05-24 07:47:38.020: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 07:47:38.020: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 07:47:38.021: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 2
2025-05-24 07:47:38.021: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 07:47:38.021: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 07:47:38.029: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 1
2025-05-24 07:47:38.034: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-05-24 07:47:38.034: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-05-24 07:47:38.034: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 0
2025-05-24 07:47:38.039: [INFO][Sync]     Configuration completed, state: 7
2025-05-24 07:47:38.040: [INFO][Sync] Configured DataTypeManager: Ok
2025-05-24 07:47:38.045: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-05-24 08:01:58.161: [INFO][Sync] Reset engine, reason: 0
2025-05-24 08:01:58.161: [INFO][Sync] Reset engine with reason: 0
