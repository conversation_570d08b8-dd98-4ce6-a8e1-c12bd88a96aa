import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_kirana/models/order_model.dart';
import 'package:smart_kirana/services/maps_service.dart';
import 'package:smart_kirana/utils/constants.dart';

class OrderTrackingMap extends StatefulWidget {
  final OrderModel order;
  final double height;

  const OrderTrackingMap({super.key, required this.order, this.height = 250});

  @override
  State<OrderTrackingMap> createState() => _OrderTrackingMapState();
}

class _OrderTrackingMapState extends State<OrderTrackingMap> {
  GoogleMapController? _mapController;
  final MapsService _mapsService = MapsService();

  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  bool _isLoading = true;
  String? _error;
  StreamSubscription<LatLng>? _locationSubscription;

  // Route information
  String? _estimatedTime;
  String? _distance;
  List<String> _directions = [];

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _initializeMap() async {
    try {
      // Initialize maps service
      final initialized = await _mapsService.initialize();
      if (!initialized) {
        setState(() {
          _error = _mapsService.error ?? 'Failed to initialize maps';
          _isLoading = false;
        });
        return;
      }

      // Check if order has delivery coordinates
      if (widget.order.deliveryLatitude == null ||
          widget.order.deliveryLongitude == null) {
        setState(() {
          _error = 'Delivery location not available';
          _isLoading = false;
        });
        return;
      }

      await _setupBasicMapData();

      // Start simulating delivery agent movement if order is shipped
      if (widget.order.status == OrderStatus.shipped) {
        _startLocationSimulation();
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error setting up map: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _setupBasicMapData() async {
    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    LatLng? currentLocation;
    if (widget.order.currentLatitude != null &&
        widget.order.currentLongitude != null) {
      currentLocation = LatLng(
        widget.order.currentLatitude!,
        widget.order.currentLongitude!,
      );
    }

    // Create markers using maps service
    _markers = _mapsService.createOrderTrackingMarkers(
      deliveryLocation: deliveryLocation,
      currentLocation: currentLocation,
      deliveryAgentName: widget.order.deliveryAgentName,
    );

    // Get route if current location is available
    if (currentLocation != null) {
      try {
        final routeData = await _mapsService.getRoute(
          origin: currentLocation,
          destination: deliveryLocation,
        );

        if (routeData != null) {
          final route = routeData['routes'][0];
          final polylinePoints = route['overview_polyline']['points'];
          final routePoints = _mapsService.decodePolyline(polylinePoints);

          _polylines = _mapsService.createRoutePolyline(routePoints);

          // Extract route information
          final leg = route['legs'][0];
          _distance = leg['distance']['text'];
          _estimatedTime = leg['duration']['text'];

          // Extract directions
          _directions = [];
          if (leg['steps'] != null) {
            for (var step in leg['steps']) {
              final instruction = step['html_instructions']
                  .toString()
                  .replaceAll(RegExp(r'<[^>]*>'), ''); // Remove HTML tags
              _directions.add(instruction);
            }
          }
        } else {
          _polylines.clear();
          _distance = null;
          _estimatedTime = null;
          _directions.clear();
        }
      } catch (e) {
        // If route calculation fails, just clear polylines
        _polylines.clear();
        _distance = null;
        _estimatedTime = null;
        _directions.clear();
      }
    } else {
      _polylines.clear();
      _distance = null;
      _estimatedTime = null;
      _directions.clear();
    }
  }

  void _startLocationSimulation() {
    if (widget.order.deliveryLatitude == null ||
        widget.order.deliveryLongitude == null) {
      return;
    }

    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    // Store location: Center Point, Aligarh
    const storeLocation = LatLng(27.8974, 78.0880); // Aligarh coordinates

    _locationSubscription = _mapsService
        .simulateDeliveryAgentMovement(
          startLocation: storeLocation,
          endLocation: deliveryLocation,
          interval: const Duration(
            seconds: 10,
          ), // Slower updates to reduce reloads
        )
        .listen((newLocation) {
          _updateDeliveryAgentLocation(newLocation);
        });
  }

  void _updateDeliveryAgentLocation(LatLng newLocation) {
    // Update markers without setState to avoid full screen reload
    _markers.removeWhere(
      (marker) => marker.markerId.value == 'current_location',
    );
    _markers.add(
      Marker(
        markerId: const MarkerId('current_location'),
        position: newLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: InfoWindow(
          title: widget.order.deliveryAgentName ?? 'Delivery Agent',
          snippet: 'Current location',
        ),
      ),
    );

    // Update route with new location
    _updateRouteToNewLocation(newLocation);

    // Only call setState once after all updates
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _updateRouteToNewLocation(LatLng newLocation) async {
    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    try {
      final routeData = await _mapsService.getRoute(
        origin: newLocation,
        destination: deliveryLocation,
      );

      if (routeData != null) {
        final route = routeData['routes'][0];
        final polylinePoints = route['overview_polyline']['points'];
        final routePoints = _mapsService.decodePolyline(polylinePoints);

        _polylines = _mapsService.createRoutePolyline(routePoints);

        // Update route information
        final leg = route['legs'][0];
        _distance = leg['distance']['text'];
        _estimatedTime = leg['duration']['text'];

        // Update directions
        _directions = [];
        if (leg['steps'] != null) {
          for (var step in leg['steps']) {
            final instruction = step['html_instructions'].toString().replaceAll(
              RegExp(r'<[^>]*>'),
              '',
            ); // Remove HTML tags
            _directions.add(instruction);
          }
        }
      }
    } catch (e) {
      // If route update fails, keep existing route
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Map Card
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
          child: Container(
            height: widget.height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppBorderRadius.medium),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppBorderRadius.medium),
              child: _buildMapContent(),
            ),
          ),
        ),

        // Route Information Panel
        if (!_isLoading && _error == null && _estimatedTime != null)
          const SizedBox(height: AppPadding.small),
        if (!_isLoading && _error == null && _estimatedTime != null)
          _buildRouteInfoPanel(),
      ],
    );
  }

  Widget _buildMapContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppPadding.small),
            Text('Loading map...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: AppColors.error),
            const SizedBox(height: AppPadding.small),
            Text(
              'Map Error',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppPadding.small / 2),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppPadding.medium,
              ),
              child: Text(
                _error!,
                style: AppTextStyles.bodySmall,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: AppPadding.medium),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _initializeMap();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (widget.order.deliveryLatitude == null ||
        widget.order.deliveryLongitude == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.location_off,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppPadding.small),
            Text(
              'Location Not Available',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppPadding.small / 2),
            Text(
              'Delivery location will be updated soon',
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    LatLng? currentLocation;
    if (widget.order.currentLatitude != null &&
        widget.order.currentLongitude != null) {
      currentLocation = LatLng(
        widget.order.currentLatitude!,
        widget.order.currentLongitude!,
      );
    }

    final initialCameraPosition = _mapsService.getCameraPositionForBounds(
      deliveryLocation: deliveryLocation,
      currentLocation: currentLocation,
    );

    return GoogleMap(
      initialCameraPosition: initialCameraPosition,
      markers: _markers,
      polylines: _polylines,
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      myLocationEnabled: false,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: true,
      mapToolbarEnabled: false,
      compassEnabled: true,
      trafficEnabled: false,
      buildingsEnabled: true,
      indoorViewEnabled: false,
      mapType: MapType.normal,
      // Add error handling for map creation
      onCameraMove: (CameraPosition position) {
        // Handle camera movement if needed
      },
    );
  }

  Widget _buildRouteInfoPanel() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.route, color: AppColors.primary),
                const SizedBox(width: AppPadding.small),
                Text('Route Information', style: AppTextStyles.heading3),
              ],
            ),
            const SizedBox(height: AppPadding.medium),

            // Distance and Time Row
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.straighten,
                    'Distance',
                    _distance ?? 'Calculating...',
                  ),
                ),
                const SizedBox(width: AppPadding.medium),
                Expanded(
                  child: _buildInfoItem(
                    Icons.access_time,
                    'Estimated Time',
                    _estimatedTime ?? 'Calculating...',
                  ),
                ),
              ],
            ),

            // Store Location Info
            const SizedBox(height: AppPadding.medium),
            _buildInfoItem(
              Icons.store,
              'Store Location',
              'Center Point, Aligarh',
            ),

            // Directions (if available and not too many)
            if (_directions.isNotEmpty) ...[
              const SizedBox(height: AppPadding.medium),
              Row(
                children: [
                  const Icon(
                    Icons.directions,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: AppPadding.small),
                  Text(
                    'Next Steps',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppPadding.small),
              ...(_directions
                  .take(3)
                  .map(
                    (direction) => Padding(
                      padding: const EdgeInsets.only(
                        bottom: AppPadding.small / 2,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.arrow_forward,
                            size: 16,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(width: AppPadding.small),
                          Expanded(
                            child: Text(
                              direction,
                              style: AppTextStyles.bodySmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )),
              if (_directions.length > 3)
                Text(
                  '... and ${_directions.length - 3} more steps',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: AppColors.primary),
        const SizedBox(width: AppPadding.small),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
