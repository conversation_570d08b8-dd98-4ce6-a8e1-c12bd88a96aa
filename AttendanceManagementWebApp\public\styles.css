/* Reset some default styling */
body, h1, h2, a {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    text-align: center;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    /* background-color: #f4f4f4; */
    background-image:radial-gradient(#727b7f7d, #ffffff);
}

/* Main container */
.container {
    min-width: 25%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

/* University Logo */
.logo {
    max-width: 350px;
    height: auto;
    margin-bottom: 20px;
}
.landingPage {
    text-decoration: none;
}

/* Welcome and Login Heading */
.welcome-heading, .login-heading {
    font-size: 44px;
    color: #666;
    margin-bottom: 20px;
}

/* Login Form */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    max-width: 300px;
}

/* Input Fields */
.login-form input {
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

/* Forgot Password Link */
.forgot-password {
    font-size: 14px;
    color: #007bff;
    text-decoration: none;
    margin-top: 10px;
    transition: color 0.3s ease;
    text-align: center;
}

.forgot-password:hover {
    color: #0056b3;
}

/* login-prompt Signup Prompt Styling */
.login-prompt, .signup-prompt {
    text-align: center;
}

.login-prompt h4, .signup-prompt h4 {
    font-size: 16px;
    color: #555;
}

.login-prompt a, .signup-prompt a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

.login-prompt a:hover, .signup-prompt a:hover {
    color: #0056b3;
}

/* Button Container */
.button-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
    margin-top: 20px;
}

/* Buttons */
.button {
    padding: 15px 20px;
    min-width: 300px;
    background-color: #007bff;
    color: #fff;
    text-decoration: none;
    border: none;
    border-radius: 10px;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.button:hover {
    background-color: #0056b3;
}

/* Navbar Styling */
.Dashboard-container {
    width: 100%;
}
.navbar {
    /* background-color: #b8bfc7; */
    background-color:rgba(184, 191, 199, 0.55);
    position: sticky;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
}

.navbar .logo {
    max-width: 200px;
    margin-top: 15px;
    height: auto;
}

/* Dropdown Styling */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropbtn {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}

.dropbtn:hover {
    background-color: #0056b3;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    z-index: 1;
}

.dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    border-bottom: 1px solid #ddd;
}

.dropdown-content a:last-child {
    border-bottom: none;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
}


/* For student image in dropdown menu */
.student-image {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

/* Add styles for dropdown button and content */
.navbar .dropdown .dropbtn {
    display: flex;
    align-items: center;
    background-color: #007bff;
    color: white;
    padding: 10px;
    font-size: 16px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}

.navbar .dropdown .dropdown-content {
    display: none;
    position: absolute;
    min-width: 180px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
}

.navbar .dropdown:hover .dropdown-content {
    display: block;
}

/* Hero Section with Slideshow */
.hero-section {
    width: 100%;
    overflow: hidden;
    background-color: #f4f4f4;
}

.slideshow-container {
    position: relative;
    max-width: 100%;
    /* max-height: 75vh; */
    overflow: hidden;
}

.slide {
    display: none;
    width: 100%;
}

.slide-image {
    width: 100%;
    /* max-height: 75vh; */
}

/* Cards Section */
.cards-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin: 40px 0;
    background-color: #f4f4f4;
    padding: 20px;
}

.card {
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    width: 400px;
    text-align: center;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-10px);
}

.card h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.card p {
    font-size: 16px;
    color: #666;
}

.card a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
    transform: color 0.3s ease;
}

.card a:hover {
    /* text-decoration: underline; */
    color: #0056b3;
}

/* Profile Page Styling */
.profile-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 75vh;
    background-image: radial-gradient(#727b7f7d, #ffffff);
}

.profile-card {
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 30px;
    width: 400px;
    text-align: left;
}

.profile-heading {
    font-size: 28px;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.profile-details p {
    font-size: 16px;
    color: #555;
    margin-bottom: 10px;
}

.profile-details strong {
    color: #333;
}

footer {
    border-top: 2px solid #007bff ;
    width: 100%;
}

footer p a{
    text-decoration: none;
    color: #007bff;
    transition: color 0.3s ease;
}

footer p a:hover {
    color: #0056b3;
}

.footerFlex {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Dynamic */
/* Student item styling for list */
.student-header {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    padding: 10px;
    border-bottom: 2px solid #ccc;
}

.student-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 5px;
}

/* Enrollment number and student name take up the main space */
.student-enrollment {
    width: 20%; /* Fixed width for enrollment number */
    text-align: center;
}

.student-faculty {
    width: 20%; /*Fixed width for enrollment number */
    text-align: center;
}

.student-name {
    width: 20%;
}

/* Toggle container for fixed width to ensure alignment */
.attendance-container {
    width: 60px; /* Fixed width to align all toggle buttons symmetrically */
    display: flex;
    justify-content: flex-end; /* Align toggle button to the right */
}

.student-item p {
    margin: 0;
}

/* Toggle Switch for Attendance */
.attendance-toggle {
    position: relative;
    display: inline-block;
    width: 34px;
    height: 20px;
}

.attendance-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 50px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 12px;
    width: 12px;
    border-radius: 50%;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(14px);
}

/* Styling for form */
#detained-form, #attendance-form, #attendance-report-form {
    display: flex;
    flex-direction: row ;
    gap: 10px;
    margin-bottom: 20px;
}

#detained-form label, #attendance-form label, #attendance-report-form label {
    font-weight: bold;
}

#detained-form input, #detained-form select, #attendance-form input, #attendance-form select, #attendance-report-form input, #attendance-report-form select {
    padding: 6px;
    font-size: 14px;
    width: 30%;
    max-width: 300px;
    margin: 0 auto;
}

/* Styling for attendance table */
#attendance-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

#attendance-table th, #attendance-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
}

#attendance-table th {
    background-color: #007bff;
    color: white;
}

#attendance-table tfoot td {
    font-weight: bold;
    background-color: #f4f4f4;
}


/* Responsive Design */
/* Small Phone size screen  */
@media (max-width: 576px) {
    .container {
        min-width: 50%;
    }

    #detained-form, #attendance-form, #attendance-report-form {
        display: flex;
        flex-direction: column;
    }

    .student-enrollment {
        width: 100px; /* Smaller width on small screens */
    }

    .attendance-toggle {
        width: 40px;
        height: 22px;
    }

    .slider:before {
        width: 18px;
        height: 18px;
    }

    .logo {
        width: 240px;
    }

    .welcome-heading, .login-heading {
        font-size: 36px;
    }

    .button {
        padding: 8px 16px;
        min-width: 210px;
    }

    .cards-section {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .card {
        min-width: 300px;
        transition: transform 0.3s ease;
    }
    
    .card:hover {
        transform: translateX(-10px);
    }
    .slideshow-container, .slide-image {
        min-height: 40vh;
    }

}

/* large Phone size screens */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        min-width: 50%;
    }

    #detained-form, #attendance-form, #attendance-report-form {
        display: flex;
        flex-direction: column;
    }

    .slideshow-container, .slide-image {
        max-height: 40vh;
    }
}

/* Tablet size screens */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        min-width: 50%;
    }

    .slideshow-container, .slide-image {
        max-height: 40vh;
    }
}

/* Laptop size screens */
@media (min-width: 992px) and (max-width: 1200px) {
    .container {
        min-width: 40%;
    }

    .slideshow-container, .slide-image {
        max-height: 60vh;
    }
}

/* Large size screen */
@media (min-width: 1200px) {
    .container {
        max-width: 50%;
    }

    .slideshow-container, .slide-image {
        max-height: 70vh;
    }
}