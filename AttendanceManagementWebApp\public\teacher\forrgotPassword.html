<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Link CSS  -->
    <link rel="stylesheet" href="../styles.css">
    
    <!-- Link Favicon  -->
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
    
    <title>Forgot Password - AMU Attendance Portal</title>
    <style>
        .error-message, .success-message {
            font-size: 0.9em;
            margin-top: 5px;
            display: none; /* Initially hidden */
        }
        .error-message {
            color: red;
        }
        .success-message {
            color: green;
        }
    </style>
</head>
<body>
    <div class="footerFlex">
        <div class="container">
            <!-- logo with name  -->
            <a href="./teacherLogin.html" class="logo"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>

            <!-- Forgot Password Heading -->
            <h2 class="login-heading">Forgot Password</h2>

            <!-- Forgot Password Form -->
            <form id="forgotPasswordForm" class="login-form">
                <!-- Email Input -->
                <input type="email" id="email" name="email" placeholder="Enter your registered email" required>
                <span id="emailError" class="error-message">Email is not registered. Please do signup.</span>
                <span id="emailSuccess" class="success-message">Password reset link sent! Check your email.</span>

                <!-- Submit Button -->
                <button type="submit" class="button">Send Reset Link <i class="fa-solid fa-envelope fa-fade"></i></button>
                
                <!-- Back to Login Link -->
                <div class="login-prompt">
                    <h4><a href="./teacherLogin.html">Back to login</a></h4>
                </div>
            </form>
        </div>

        <footer style="position: absolute; bottom: 0;">
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script>
        document.getElementById("forgotPasswordForm").addEventListener("submit", async function (e) {
            e.preventDefault(); // Prevent normal form submission
    
            // Clear previous messages
            const emailError = document.getElementById("emailError");
            const emailSuccess = document.getElementById("emailSuccess");
            emailError.style.display = "none";
            emailSuccess.style.display = "block"; // Show success message temporarily
            emailSuccess.textContent = "Sending reset password link...";
    
            // Get email from the form
            const email = document.getElementById("email").value;
    
            try {
                // Send email data to backend for verification
                const response = await fetch("/forgotPassword", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ email }),
                });
    
                const result = await response.json();
    
                // Handle success or failure messages
                if (!response.ok) {
                    emailSuccess.style.display = "none"; // Hide "Sending..." message if error occurs
                    emailError.style.display = "block";
                    emailError.textContent = result.message || "Email is not registered. Please do signup.";
                } else {
                    emailSuccess.textContent = "Password reset link sent! Check your email.";
                    document.getElementById("forgotPasswordForm").reset();
                }
            } catch (error) {
                console.error("Error:", error);
                emailSuccess.style.display = "none"; // Hide "Sending..." message if error occurs
                emailError.style.display = "block";
                emailError.textContent = "Something went wrong. Please try again later.";
            }
        });
    </script>    
</body>
</html>
