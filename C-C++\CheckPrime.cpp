#include<iostream>
using namespace std;
int main()
{
    int n;
    cout<<"Enter a number that you want to check :";
    cin>>n;
    if(n==1) cout<<"1 is neither Prime nor Composite";
    else if(n<1) cout<<"Ah Ah! Invalid Input";
    else
    {
        bool a=true;
        for(int i=2; i<=n-1; i++)
        {
            if(n%i==0) a=false;
        }
        if (a==true) cout<<"Your number is Prime";
        if(a!=true) cout<<"Your number is composite";
        // if (a==true) cout<<n<<" , ";
        // j++;
    }
    return 0;
}