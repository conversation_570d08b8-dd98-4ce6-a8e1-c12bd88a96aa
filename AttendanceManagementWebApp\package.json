{"name": "attendancemanagementwebapp", "version": "1.2.0", "description": "Mini Project", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "repository": {"type": "git", "url": "github.com/mohdyusuf2312/AttendanceManagementWebApp.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "exceljs": "^4.4.0", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.2", "nodemailer": "^6.9.16"}, "devDependencies": {"dotenv": "^16.4.5", "nodemon": "^3.1.7"}}