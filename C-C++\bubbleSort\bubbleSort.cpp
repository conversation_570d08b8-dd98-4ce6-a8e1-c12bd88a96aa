#include<iostream>
using namespace std;

int main()
{
    int n;
    cout<<"Enter the number of elements:";
    cin>>n;

    int arr[n];
    cout<<"Enter the elements:";
    for(int i=0; i<n; i++){
        cin>>arr[i];
    }

    // print the original array
    for(int i=0; i<n; i++){
        cout<<arr[i]<<" ";
    }
    cout<<endl;

    // bubble sort
    for(int i=0; i<n-1; i++){
        bool flag = true;
        for(int j=0; j<n-1-i; j++){
            if(arr[j]>arr[j+1]){
                swap(arr[j], arr[j+1]);
                flag = false;
            }
        }
        if (flag == true){
            break;
        }
    }

    // print the updated array
    for(int i=0; i<n; i++){
        cout<<arr[i]<<" ";
    }
    cout<<endl;

}