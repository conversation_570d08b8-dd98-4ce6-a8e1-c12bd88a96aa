#include<iostream>
using namespace std;
struct node
{
    int data;
    struct node *next;
};

void LinkedList(struct node *head)
{
    while(head != NULL)
    {
        cout<<"Element : "<<head -> data<<endl;
        head = head -> next;
    }
}

int main()
{
    struct node *head;
    struct node *second;
    struct node *third;
    struct node *fourth;

    // Memory allocation
    head = (struct node *)malloc(sizeof(struct node));
    second = (struct node *)malloc(sizeof(struct node));
    third = (struct node *)malloc(sizeof(struct node));
    fourth = (struct node *)malloc(sizeof(struct node));

    // Linked head to second node
    head -> data = 7;
    head -> next = second;
    
    // Linked second to third node
    second -> data = 11;
    second -> next = third;

    // Linked third to fourth node
    third -> data = 14;
    third -> next = fourth;

    // Linked fourth to NUll pointer
    fourth -> data = 18;
    fourth -> next = NULL;

    LinkedList(head);

    return 0;
}