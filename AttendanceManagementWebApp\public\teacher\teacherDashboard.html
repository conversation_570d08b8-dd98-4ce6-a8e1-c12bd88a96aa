<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../styles.css">
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
    <title>Teacher Dashboard - AMU Attendance Portal</title>
</head>
<body>
    <div class="Dashboard-container">
        <nav class="navbar" style="position: absolute;">
            <div class="navbar-container">
                <a href="./teacherDashboard.html"><img src="../assets/logoWithName.png" alt="AMU Logo" class="logo"></a>
                <div class="dropdown">
                    <button class="dropbtn">
                        <img id="teacher-image" src="../assets/user'sPic.jpeg" alt="Teacher Image" class="student-image"> 
                        Teacher Name &#9662;
                    </button>
                    <div class="dropdown-content">
                        <a href="./profile.html"><i class="fa-regular fa-address-card fa-fade"></i> Profile</a>
                        <a href="#" onclick="logoutTeacher()"><i class="fa-solid fa-arrow-right-from-bracket fa-fade"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section with Image Slideshow -->
        <section class="hero-section">
            <div class="slideshow-container">
                <div class="slide fade">
                    <img src="../assets/amu1.jpg" class="slide-image" alt="AMU Image 1">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu2.jpeg" class="slide-image" alt="AMU Image 2">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu3.png" class="slide-image" alt="AMU Image 3">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu4.jpg" class="slide-image" alt="AMU Image 4">
                </div>
            </div>
        </section>

        <section class="cards-section">
            <!-- New Attendance Report Section -->
            <div class="card" style="width: 75%;">
                <h3>Student Attendance Report</h3>
                <form id="attendance-report-form">
                    <label for="report-course">Course:</label>
                    <select id="report-course" required onchange="loadReportSemesters()">
                        <option value="">Select a course</option>
                        <option value="MCA">MCA</option>
                        <option value="M.Sc Cyber">M.Sc Cyber</option>
                        <option value="B.Sc">B.Sc</option>
                    </select>
        
                    <label for="report-semester">Semester:</label>
                    <select id="report-semester" required onchange="loadReportSubjects()">
                        <option value="">Select a semester</option>
                    </select>
        
                    <label for="report-subject">Subject:</label>
                    <select id="report-subject" required>
                        <option value="">Select a subject</option>
                    </select>
                </form>
                <div>
                    <button class="button" style="font-size: medium;" onclick="generateAttendanceTable()">
                        <i class="fa-regular fa-file-excel fa-bounce"></i> Generate Attendance Report
                    </button>
                </div>
            </div>
        
            <!-- Table Container for Attendance -->
            <div class="table-container" id="attendance-table-container" style="display: none; width: 90%;">
                <h3>Attendance Table</h3>
                <table id="attendance-table">
                    <thead>
                        <tr>
                            <th>Enrollment No.</th>
                            <th>Name</th>
                            <!-- Dates will be dynamically added here -->
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Student attendance data will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <!-- Detained Student Report -->
            <div class="card" style="width: 75%;">
                <h3>Detained Student</h3>
                <form id="detained-form">
                    <label for="course">Course:</label>
                    <select id="d_course" required onchange="d_loadSemesters()">
                        <option value="">Select a course</option>
                        <option value="MCA">MCA</option>
                        <option value="M.Sc Cyber">M.Sc Cyber</option>
                        <option value="B.Sc">B.Sc</option>
                    </select>

                    <label for="semester">Semester:</label>
                    <select id="d_semester" required onchange="d_loadSubjects()">
                        <option value="">Select a semester</option>
                    </select>

                    <label for="subject">Subject:</label>
                    <select id="d_subject" required>
                        <option value="">Select a subject</option>
                    </select>
                </form>
                <div>
                    <button class="button" style="font-size: medium;" onclick="generateReport()"><i class="fa-regular fa-file-pdf fa-bounce"></i> Generate Report</button>
                </div>
            </div>

            <!-- Attendance Form -->
            <div class="card" style="width: 75%;">
                <h3>Mark Attendance</h3>
                <form id="attendance-form">
                    <label for="course">Course:</label>
                    <select id="course" required onchange="loadSemesters()">
                        <option value="">Select a course</option>
                        <option value="MCA">MCA</option>
                        <option value="M.Sc Cyber">M.Sc Cyber</option>
                        <option value="B.Sc">B.Sc</option>
                    </select>

                    <label for="semester">Semester:</label>
                    <select id="semester" required onchange="loadSubjects(), loadStudents()">
                        <option value="">Select a semester</option>
                    </select>

                    <label for="subject">Subject:</label>
                    <select id="subject" required >
                        <option value="">Select a subject</option>
                    </select>
                </form>
            </div>

            <!-- Student List Section -->
            <div class="student-list-container" id="student-list-container" style="display:none; width: 70%;">
                <div id="student-list"></div>
                <button class="button" style="font-size: medium;" onclick="submitAttendance()"><i class="fa-solid fa-check fa-fade"></i> Submit Attendance</button>
            </div>
        </section>

        <footer>
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="../script/slideshow.js"></script>
    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
    <script>
        // Logout function
        async function logoutTeacher() {
            try {
                const response = await fetch("/teacherLogout", {
                    method: "POST",
                    // credentials: "same-origin", // Ensures cookies are included
                    headers: { "Content-Type": "application/json" }
                });

                if (response.ok) {
                    // Redirect to the login page after successful logout
                    window.location.href = "./teacherLogin.html";
                } else {
                    console.error("Failed to logout.");
                    alert("Logout failed. Please try again.");
                }
            } catch (error) {
                console.error("Error during logout:", error);
                alert("An error occurred during logout.");
            }
        }

        // Fetch teacher profile data and update the dropdown button
        document.addEventListener("DOMContentLoaded", async () => {
            const response = await fetch("/teacherProfile", {
                    method: "GET",
                    credentials: "same-origin", // Ensures cookies are included in the request
                    headers: { "Content-Type": "application/json" }
                });
                teacher = await response.json();
                teacherName = teacher.name;

            document.querySelector(".dropbtn").innerHTML = `
                <img id="teacher-image" src="../assets/user'sPic.jpeg" alt="Teacher Image" class="student-image"> 
                ${teacherName} &#9662;
            `;
        });

        function loadSemesters() {
            const course = document.getElementById("course").value;
            const semesterDropdown = document.getElementById("semester");
            semesterDropdown.innerHTML = '<option value="">Select a semester</option>';

            let semesters = 4;
            if (course === "B.Sc") semesters = 8;

            for (let i = 1; i <= semesters; i++) {
                semesterDropdown.innerHTML += `<option value="${i}">Semester ${i}</option>`;
            }
        }
        
        function loadReportSemesters() {
            const course = document.getElementById("report-course").value;
            const semesterDropdown = document.getElementById("report-semester");
            semesterDropdown.innerHTML = '<option value="">Select a semester</option>';

            let semesters = 4;
            if (course === "B.Sc") semesters = 8;

            for (let i = 1; i <= semesters; i++) {
                semesterDropdown.innerHTML += `<option value="${i}">Semester ${i}</option>`;
            }
        }
        
        function d_loadSemesters() {
            const course = document.getElementById("d_course").value;
            const semesterDropdown = document.getElementById("d_semester");
            semesterDropdown.innerHTML = '<option value="">Select a semester</option>';

            let semesters = 4;
            if (course === "B.Sc") semesters = 8;

            for (let i = 1; i <= semesters; i++) {
                semesterDropdown.innerHTML += `<option value="${i}">Semester ${i}</option>`;
            }
        }

        async function loadSubjects() {
            const course = document.getElementById("course").value;
            const semester = document.getElementById("semester").value;
            const subjectDropdown = document.getElementById("subject");

            if (!course || !semester) return;  // Ensure both course and semester are selected

            try {
                const response = await fetch(`/api/subjects?course=${course}&semester=${semester}`);
                const subjects = await response.json();

                subjectDropdown.innerHTML = '<option value="">Select a subject</option>';
                subjects.forEach(subject => {
                    subjectDropdown.innerHTML += `<option value="${subject}">${subject}</option>`;
                });
            } catch (error) {
                console.error('Error loading subjects:', error);
            }
        }

        async function loadReportSubjects() {
            const course = document.getElementById("report-course").value;
            const semester = document.getElementById("report-semester").value;
            const subjectDropdown = document.getElementById("report-subject");

            if (!course || !semester) return;  // Ensure both course and semester are selected

            try {
                const response = await fetch(`/api/subjects?course=${course}&semester=${semester}`);
                const subjects = await response.json();

                subjectDropdown.innerHTML = '<option value="">Select a subject</option>';
                subjects.forEach(subject => {
                    subjectDropdown.innerHTML += `<option value="${subject}">${subject}</option>`;
                });
            } catch (error) {
                console.error('Error loading subjects:', error);
            }
        }

        async function d_loadSubjects() {
            const course = document.getElementById("d_course").value;
            const semester = document.getElementById("d_semester").value;
            const subjectDropdown = document.getElementById("d_subject");

            if (!course || !semester) return;  // Ensure both course and semester are selected

            try {
                const response = await fetch(`/api/subjects?course=${course}&semester=${semester}`);
                const subjects = await response.json();

                subjectDropdown.innerHTML = '<option value="">Select a subject</option>';
                subjects.forEach(subject => {
                    subjectDropdown.innerHTML += `<option value="${subject}">${subject}</option>`;
                });
            } catch (error) {
                console.error('Error loading subjects:', error);
            }
        }

        async function loadStudents() {
            const course = document.getElementById("course").value;
            const semester = document.getElementById("semester").value;

            const response = await fetch(`/api/students?course=${course}&semester=${semester}`);
            const students = await response.json();

            // Sort students by faculty_number
            students.sort((a, b) => a.faculty_number.localeCompare(b.faculty_number));

            const studentListContainer = document.getElementById("student-list");
            studentListContainer.innerHTML = '';

            // Add header row
            const headerRow = document.createElement("div");
            headerRow.className = "student-header";
            headerRow.innerHTML = `
                <strong>Enrollment Number</strong>
                <strong>Faculty Number</strong>
                <strong>Name</strong>
                <strong>Status</strong>
            `;
            studentListContainer.appendChild(headerRow);

            // Add student rows
            students.forEach(student => {
                const studentItem = document.createElement("div");
                studentItem.className = "student-item";
                studentItem.innerHTML = `
                    <p class="student-enrollment">${student.enrollment_number}</p>
                    <p class="student-faculty">${student.faculty_number}</p>
                    <p class="student-name">${student.first_name + " " + student.last_name}</p>
                    <div class="attendance-container">
                        <label class="attendance-toggle">
                            <input type="checkbox" class="attendance-checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                `;
                studentListContainer.appendChild(studentItem);
            });

            document.getElementById("student-list-container").style.display = "block";
        }

        async function submitAttendance() {
            const course = document.getElementById("course").value;
            const semester = document.getElementById("semester").value;
            const subject = document.getElementById("subject").value;

            // Check if subject is selected
            if (!subject) {
                alert("Please select a subject");
                return; // Stop further execution if subject is not selected
            }

            const studentItems = document.querySelectorAll(".student-item");
            const attendanceData = Array.from(studentItems).map(item => ({
                enrollment_number: item.querySelector(".student-enrollment").innerText,
                present: item.querySelector(".attendance-checkbox").checked
            }));

            const response = await fetch("/api/attendance", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ course, semester, subject, teacher_name: teacherName, attendance: attendanceData })
            });

            if (response.ok) {
                alert("Attendance successfully submitted.");

                // Reset the form and hide the student list container
                document.getElementById("attendance-form").reset();
                document.getElementById("student-list-container").style.display = "none";
            } else {
                alert("Failed to submit attendance.");
            }
        }

        async function generateReport() {
            const course = document.getElementById("d_course").value;
            const semester = document.getElementById("d_semester").value;
            const subject = document.getElementById("d_subject").value;

            if (!course || !semester || !subject) {
                alert("Please select course, semester, and subject.");
                return;
            }

            try {
                const [ studentRes, teacherRes] = await Promise.all([
                    fetch(`/api/attendanceReport?course=${course}&semester=${semester}&subject=${subject}`),
                    fetch(`/teacherProfile`)
                ]);
                
                if (!studentRes.ok || !teacherRes.ok) {
                    throw new Error("Error fetching data");
                }
                
                // Backend provides the list of detained students directly
                const detainedStudents = await studentRes.json();
                const teacher = await teacherRes.json();
                const teacherDepartment = teacher.department;

                // Generate the PDF with jsPDF
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // PDF Filename
                const fileName = `DetainedStudent_${subject}.pdf`;

                // Styling and Content
                const pageWidth = doc.internal.pageSize.getWidth();
                
                // Heading
                doc.setFontSize(16);
                doc.text("Aligarh Muslim University", pageWidth / 2, 20, { align: "center" });
                
                // Subheading: Department
                doc.setFontSize(12);
                doc.text(`${teacherDepartment} Department`, pageWidth / 2, 30, { align: "center" });
                
                // Course, Semester, and Subject Information
                doc.setFontSize(10);
                doc.text(`Course: ${course} | Semester: ${semester} | Subject: ${subject}`, pageWidth / 2, 40, { align: "center" });
                
                // List Heading
                doc.setFontSize(14);
                doc.text("List of detained students with attendance below 60%.", pageWidth / 2, 50, { align: "center" });
                
                // Table Headers
                const startX = 10;
                let yPosition = 60;
                
                doc.setFontSize(10);
                doc.text("Sr. No.", startX, yPosition);
                doc.text("Enrollment No.", startX + 20, yPosition);
                doc.text("Name", startX + 60, yPosition);
                doc.text("Attendance (%)", startX + 130, yPosition);

                // Draw a line below headers
                yPosition += 5;
                doc.line(10, yPosition, pageWidth - 10, yPosition);

                // Table Rows
                detainedStudents.forEach((student, index) => {
                    yPosition += 10;
                    doc.text(String(index + 1), startX, yPosition);
                    doc.text(`${student.enrollment_number}`, startX + 20, yPosition);
                    doc.text(`${student.name}`, startX + 60, yPosition);
                    doc.text(`${student.attendancePercentage}%`, startX + 130, yPosition);
                });

                // Save the PDF
                doc.save(fileName);
            } catch (error) {
                console.error("Error generating report:", error);
                alert("Error generating report.");
            }
        }

        async function generateAttendanceTable() {
            const course = document.getElementById("report-course").value;
            const semester = document.getElementById("report-semester").value;
            const subject = document.getElementById("report-subject").value;

            if (!course || !semester || !subject) {
                alert("Please select course, semester, and subject.");
                return;
            }

            try {
                // Request the Excel file
                const response = await fetch(`/api/attendanceTable?course=${course}&semester=${semester}&subject=${subject}`);
                const data = await response.blob(); // Get the file as a blob

                // Create a URL for the blob and initiate download
                const downloadUrl = window.URL.createObjectURL(data);
                const a = document.createElement("a");
                a.href = downloadUrl;
                a.download = `${course}_${semester}_${subject}.xlsx`; // Set default file name
                document.body.appendChild(a);
                a.click();

                // Clean up
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);
            } catch (error) {
                console.error("Error generating attendance table:", error);
                alert("Error generating attendance table.");
            }
        }
    </script>
</body>
</html>
