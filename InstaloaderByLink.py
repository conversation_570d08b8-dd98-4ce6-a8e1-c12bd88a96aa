import instaloader

# Instaloader object
L = instaloader.Instaloader()

# Login karna ho to yahan credentials da<PERSON> (optional)
# L.login("your_username", "your_password")

# File read
with open("instagram_links.txt", "r") as file:
    links = file.readlines()

# Clean links
links = [link.strip() for link in links if link.strip()]

# Download each post
for link in links:
    try:
        shortcode = link.strip("/").split("/")[-1]
        print(f"Downloading: {shortcode}")
        post = instaloader.Post.from_shortcode(L.context, shortcode)
        L.download_post(post, target=f"downloads/{shortcode}")
    except Exception as e:
        print(f"Error downloading {link}: {e}")
