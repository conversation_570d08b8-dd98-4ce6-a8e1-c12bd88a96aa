<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Link CSS  -->
    <link rel="stylesheet" href="../styles.css">
    
    <!-- Link Favicon  -->
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
    
    <title>Reset Password - AMU Attendance Portal</title>
    <style>
        .error-message, .success-message {
            font-size: 0.9em;
            margin-top: 5px;
            display: none; /* Initially hidden */
        }
        .error-message {
            color: red;
        }
        .success-message {
            color: green;
        }
    </style>
</head>
<body>
    <div class="footerFlex">
        <div class="container">
            <!-- logo with name  -->
            <a href="../index.html" class="logo"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>

            <!-- Reset Password Heading -->
            <h2 class="login-heading">Reset Password</h2>

            <!-- Reset Password Form -->
            <form id="resetPasswordForm" class="login-form">
                <!-- New Password Input -->
                <input type="password" id="newPassword" name="newPassword" placeholder="Enter new password" required>
                <span id="passwordError" class="error-message">Password should be at least 6 characters.</span>

                <!-- Confirm Password Input -->
                <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm new password" required>
                <span id="confirmPasswordError" class="error-message">Passwords do not match.</span>

                <!-- Success Message -->
                <span id="resetSuccess" class="success-message">Password has been reset successfully. Redirecting to login...</span>

                <!-- Submit Button -->
                <button type="submit" class="button">Reset Password <i class="fa-solid fa-lock fa-fade"></i></button>
            </form>
        </div>

        <footer style="position: absolute; bottom: 0;">
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script>
        document.getElementById("resetPasswordForm").addEventListener("submit", async function (e) {
            e.preventDefault(); // Prevent normal form submission

            // Clear previous messages
            document.getElementById("passwordError").style.display = "none";
            document.getElementById("confirmPasswordError").style.display = "none";
            document.getElementById("resetSuccess").style.display = "none";

            // Get form data
            const newPassword = document.getElementById("newPassword").value;
            const confirmPassword = document.getElementById("confirmPassword").value;

            // Validate password match
            if (newPassword.length < 6) {
                document.getElementById("passwordError").style.display = "block";
                return;
            }

            if (newPassword !== confirmPassword) {
                document.getElementById("confirmPasswordError").style.display = "block";
                return;
            }

            // Extract token from URL query parameter
            const pathSegments = window.location.pathname.split('/');
            const token = pathSegments[pathSegments.length - 1]; // Last segment is the token

            // Send new password to the backend
            try {
                const response = await fetch(`/resetPassword/${token}`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ token, newPassword }),
                });

                const result = await response.json();

                if (!response.ok) {
                    // Display any server-provided error message
                    document.getElementById("confirmPasswordError").style.display = "block";
                    document.getElementById("confirmPasswordError").textContent = result.message || "Unable to reset password.";
                } else {
                    // Success message
                    document.getElementById("resetSuccess").style.display = "block";
                    setTimeout(() => {
                        window.location.href = "../teacher/teacherLogin.html";
                    }, 2000); // Redirect to login after 3 seconds
                }
            } catch (error) {
                console.error("Error:", error);
            }
        });
    </script>
</body>
</html>
