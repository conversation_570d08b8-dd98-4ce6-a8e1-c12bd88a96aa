#include<stdio.h>
int main()
{
    int nl, da,days, power_pipes, water_pipes, crew_member, sptlight, no_truckloads;
    float wp_rs, pp_rs, cost_labor, spotlight_cost, cost_asphalt;
    double rlm, total_cost_pr;
    

    printf("Enter Road Project Length in Miles\t");
    scanf("%lf", &rlm);
    printf("\n Enter Number of Lanes\t");
    scanf("%d", &nl);
    printf("Enter Depth of Asphalt in Inches\t");
    scanf("%d", &da);
    printf("Enter Days to Complete Projects\t");
    scanf("%d", days);

    power_pipes = (5280 * rlm)/ 20;
    water_pipes = (5280 * rlm)/15;
    crew_member = (50 * rlm * nl)/ days;
    printf("=== Amounts of Materials Needed ====\n");
    printf("TruckLoads of Asphalt: %d\n",no_truckloads);
    printf("Spotlights: %d\n", sptlight);
    printf("Water Pipes :%d\n", water_pipes);
    printf("Power Pipes : %d\n", power_pipes);
    printf("Crew members needed: %d\n", crew_member);

    spotlight_cost = sptlight * 32000;
    wp_rs = water_pipes* 280;
    pp_rs = power_pipes *350;
    cost_labor = crew_member * 24 * 8 * days;
    cost_asphalt = no_truckloads * 5 * 250;
    printf("\n=== Cost of Materials ===\n");
    printf("Cost of Asphalt: $%f\n", cost_asphalt);
    printf("Cost of Spotlights : $%f\n", spotlight_cost);
    printf("Cost of Water Pipes : $%f\n",wp_rs);
    printf("Cost of Power Pipes : $%f\n", pp_rs);
    printf("Cost of Labor  : $%f\n", cost_labor);

    printf("=== Total Cost of Projects ===\n");
    printf("Total cost of projects : $ %lf\n", total_cost_pr);
    return 0;
}
