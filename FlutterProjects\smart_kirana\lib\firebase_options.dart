// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAmq5-jY4CUtsoazZmPzQzozKuTApWOWaU',
    appId: '1:158155403054:web:d5251ef2601fc5b03494bf',
    messagingSenderId: '158155403054',
    projectId: 'smart-kirana-81629',
    authDomain: 'smart-kirana-81629.firebaseapp.com',
    storageBucket: 'smart-kirana-81629.firebasestorage.app',
    measurementId: 'G-41603FC82W',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAyMcv21Wrc00fyc8xVKF1Zdm36NrUR5X4',
    appId: '1:158155403054:android:5cf64588b9b192f13494bf',
    messagingSenderId: '158155403054',
    projectId: 'smart-kirana-81629',
    storageBucket: 'smart-kirana-81629.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC8L8_F_afZ2xMWAdGeOYjPDiuLg6yP2Ao',
    appId: '1:158155403054:ios:d4aa9f0f9b8d931b3494bf',
    messagingSenderId: '158155403054',
    projectId: 'smart-kirana-81629',
    storageBucket: 'smart-kirana-81629.firebasestorage.app',
    iosClientId:
        '158155403054-jobf8q521mht6k9na7rlmr4e66ch8ddb.apps.googleusercontent.com',
    iosBundleId: 'com.example.smartKirana',
  );
}
