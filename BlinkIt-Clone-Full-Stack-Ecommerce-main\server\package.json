{"name": "server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.4.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.4", "resend": "^3.5.0", "stripe": "^16.12.0"}}