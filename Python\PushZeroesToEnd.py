arr = [1, 0, 2, 0, 4, 0, 0, 2, 45, 45, 0, 3, 0, 4]
i = len(arr) -1
j = len(arr) -1

while i >= 0:
    if arr[i] == 0:
        arr[i], arr[j] = arr[j], arr[i]
        j -= 1
    i -= 1

print(arr)


# Even numbers to the right end of the array

# arr = list(map(int, input("Enter elements of array: ").split()))
# i = len(arr) - 1
# j = len(arr) - 1

# while i >= 0:
#     if arr[i] % 2 == 0:
#         arr[i], arr[j] = arr[j], arr[i]
#         j -= 1
#     i -= 1

# print(arr)