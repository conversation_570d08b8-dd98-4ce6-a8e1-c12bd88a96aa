<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Date Wise Attendance Report - AMU Attendance Portal</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
</head>
<body>
    <div class="Dashboard-container">
        <!-- Navigation Bar -->
        <nav class="navbar" style="position: absolute;">
            <div class="navbar-container">
                <a href="./studentDashboard.html"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>
                <div class="dropdown">
                    <button class="dropbtn">
                        <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image">
                        Student Name &#9662;
                    </button>
                    <div class="dropdown-content">
                        <a href="./profile.html"><i class="fa-regular fa-address-card fa-fade"></i> Profile</a>
                        <a href="#" onclick="logoutStudent()"><i class="fa-solid fa-arrow-right-from-bracket fa-fade"></i> <i class="fa-solid fa-person-walking fa-fade"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section with Image Slideshow -->
        <section class="hero-section">
            <div class="slideshow-container">
                <div class="slide fade">
                    <img src="../assets/amu1.jpg" class="slide-image" alt="AMU Image 1">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu2.jpeg" class="slide-image" alt="AMU Image 2">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu3.png" class="slide-image" alt="AMU Image 3">
                </div>
                <div class="slide fade">
                    <img src="../assets/amu4.jpg" class="slide-image" alt="AMU Image 4">
                </div>
            </div>
        </section>

        <!-- Date Wise Attendance Card Section -->
        <section class="cards-section">
            <div class="card" style="min-width: 75%;">
                <h3>Date Wise Attendance Report</h3>
                <form id="attendance-form">
                    <!-- Date Fields -->
                    <label for="from-date">From Date:</label>
                    <input type="date" id="from-date" name="from-date" required>

                    <label for="to-date">To Date:</label>
                    <input type="date" id="to-date" name="to-date" required>

                    <!-- Subject Dropdown -->
                    <label for="subject-dropdown">Select Subject:</label>
                    <select id="subject-dropdown" name="subject-dropdown" required>
                        <option value="">Select a subject</option>
                        <!-- Subjects will be dynamically added here -->
                    </select>
                </form>

                <!-- Attendance Table -->
                <table id="attendance-table">
                    <thead>
                        <tr>
                            <th>Course Name</th>
                            <th>Date</th>
                            <th>Attendance Status</th>
                        </tr>
                    </thead>
                    <tbody id="attendance-data">
                        <!-- Attendance data will be dynamically generated here -->
                    </tbody>
                </table>
            </div>
        </section>

        <footer>
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script src="../script/slideshow.js"></script>
    <script>
        // Logout function
        async function logoutStudent() {
            try {
                const response = await fetch("/studentLogout", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" }
                });

                if (!response.ok) {
                    throw new Error("Failed to logout");
                }

                window.location.href = "./studentLogin.html";
            } catch (error) {
                console.error("Error during logout:", error);
                alert("An error occurred during logout.");
            }
        }

        // Fetch the student name from database and display it in the navbar
        // and populate the dropdown with subjects
        document.addEventListener("DOMContentLoaded", async () => {
            try {
                const response = await fetch("/api/student-profile", {
                        method: "GET",
                        credentials: "same-origin", // Ensures cookies are included in the request
                        headers: { "Content-Type": "application/json" }
                    });
                    student = await response.json();
                    studentName = student.name;

                document.querySelector(".dropbtn").innerHTML = `
                    <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image"> 
                    ${studentName} &#9662;
                `;
            } catch(error) {
                console.error('Error fetching student:', error);
            }
            try {
                // populate the dropdown with subject
                const subjectResponse = await fetch(`/api/subjects?course=${student.course}&semester=${student.semester}`, {
                    method: "GET",
                    credentials: "same-origin", // Ensures cookies are included in the request
                    headers: { "Content-Type": "application/json" }
                });
                const subjects = await subjectResponse.json();
                const subjectDropdown = document.getElementById('subject-dropdown');
                subjectDropdown.innerHTML = '<option value="">Select a subject</option>';
                subjects.forEach(subject => {
                    subjectDropdown.innerHTML += `<option value="${subject}">${subject}</option>`;
                });
            } catch (error) {
                console.error('Error loading subjects:', error);
            }
        });

        // Function to fetch attendance data based on date range and subject
        function fetchAttendanceData() {
            const fromDate = document.getElementById('from-date').value;
            const toDate = document.getElementById('to-date').value;
            const selectedSubject = document.getElementById('subject-dropdown').value;

            if (!fromDate || !toDate || !selectedSubject) {
                alert('Please select both dates and a subject.');
                return;
            }

            fetch(`/api/date-wise-attendance?from=${fromDate}&to=${toDate}&subject=${selectedSubject}`)
                .then(response => response.json())
                .then(data => {
                    const attendanceDataElement = document.getElementById('attendance-data');
                    attendanceDataElement.innerHTML = ''; // Clear previous data

                    // Populate table with attendance data
                    data.forEach(attendance => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${attendance.subject}</td>
                            <td>${attendance.date}</td>
                            <td>${attendance.status}</td>
                        `;
                        attendanceDataElement.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error fetching attendance data:', error);
                });
                document.getElementById('from-date').addEventListener('change', fetchAttendanceData);
                document.getElementById('to-date').addEventListener('change', fetchAttendanceData);
        }

        // Event listener to automatically fetch attendance data when subject is selected
        document.getElementById('subject-dropdown').addEventListener('change', fetchAttendanceData);
    </script>
</body>
</html>
