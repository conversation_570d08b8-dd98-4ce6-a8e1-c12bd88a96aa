import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/delivery_tracking_model.dart';
import '../../models/order_model.dart';
import '../../services/real_time_tracking_service.dart';
import '../../utils/constants.dart';

class EnhancedOrderTrackingMap extends StatefulWidget {
  final OrderModel order;
  final double height;
  final bool showFullScreen;

  const EnhancedOrderTrackingMap({
    super.key,
    required this.order,
    this.height = 300,
    this.showFullScreen = false,
  });

  @override
  State<EnhancedOrderTrackingMap> createState() =>
      _EnhancedOrderTrackingMapState();
}

class _EnhancedOrderTrackingMapState extends State<EnhancedOrderTrackingMap>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  final RealTimeTrackingService _trackingService = RealTimeTrackingService();

  StreamSubscription<DeliveryTrackingModel?>? _trackingSubscription;
  DeliveryTrackingModel? _currentTracking;

  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};

  bool _isLoading = true;
  String? _error;

  // Animation controllers
  late AnimationController _pulseController;
  late AnimationController _routeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _routeAnimation;

  // Custom marker icons
  BitmapDescriptor? _storeIcon;
  BitmapDescriptor? _deliveryAgentIcon;
  BitmapDescriptor? _customerIcon;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeMap();
  }

  @override
  void dispose() {
    _trackingSubscription?.cancel();
    _pulseController.dispose();
    _routeController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _routeController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _routeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _routeController, curve: Curves.easeInOut),
    );
  }

  Future<void> _initializeMap() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Initialize tracking service
      final initialized = await _trackingService.initialize();
      if (!initialized) {
        setState(() {
          _error = 'Failed to initialize tracking service';
          _isLoading = false;
        });
        return;
      }

      // Load custom markers
      await _loadCustomMarkers();

      // Check if order has delivery coordinates
      if (widget.order.deliveryLatitude == null ||
          widget.order.deliveryLongitude == null) {
        setState(() {
          _error = 'Delivery location not available';
          _isLoading = false;
        });
        return;
      }

      // Start listening to real-time tracking updates
      _startTrackingUpdates();
    } catch (e) {
      setState(() {
        _error = 'Error initializing map: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCustomMarkers() async {
    try {
      // Create custom markers programmatically
      _storeIcon = await _createCustomMarkerFromIcon(
        Icons.store,
        Colors.green,
        const Size(60, 60),
      );

      _deliveryAgentIcon = await _createCustomMarkerFromIcon(
        Icons.delivery_dining,
        Colors.blue,
        const Size(50, 50),
      );

      _customerIcon = await _createCustomMarkerFromIcon(
        Icons.location_on,
        Colors.red,
        const Size(50, 50),
      );
    } catch (e) {
      debugPrint('Error loading custom markers: $e');
      // Fallback to default markers
      _storeIcon = BitmapDescriptor.defaultMarkerWithHue(
        BitmapDescriptor.hueGreen,
      );
      _deliveryAgentIcon = BitmapDescriptor.defaultMarkerWithHue(
        BitmapDescriptor.hueBlue,
      );
      _customerIcon = BitmapDescriptor.defaultMarkerWithHue(
        BitmapDescriptor.hueRed,
      );
    }
  }

  Future<BitmapDescriptor> _createCustomMarkerFromIcon(
    IconData iconData,
    Color color,
    Size size,
  ) async {
    try {
      final pictureRecorder = ui.PictureRecorder();
      final canvas = Canvas(pictureRecorder);

      // Draw circle background
      final paint =
          Paint()
            ..color = color
            ..style = PaintingStyle.fill;

      final center = Offset(size.width / 2, size.height / 2);
      final radius = size.width / 2;
      canvas.drawCircle(center, radius, paint);

      // Draw icon
      final textPainter = TextPainter(textDirection: TextDirection.ltr);
      textPainter.text = TextSpan(
        text: String.fromCharCode(iconData.codePoint),
        style: TextStyle(
          fontSize: size.width * 0.6,
          fontFamily: iconData.fontFamily,
          color: Colors.white,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          (size.width - textPainter.width) / 2,
          (size.height - textPainter.height) / 2,
        ),
      );

      final picture = pictureRecorder.endRecording();
      final image = await picture.toImage(
        size.width.toInt(),
        size.height.toInt(),
      );
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      return BitmapDescriptor.bytes(byteData!.buffer.asUint8List());
    } catch (e) {
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(
        color == Colors.green
            ? BitmapDescriptor.hueGreen
            : color == Colors.blue
            ? BitmapDescriptor.hueBlue
            : BitmapDescriptor.hueRed,
      );
    }
  }

  void _startTrackingUpdates() {
    _trackingSubscription = _trackingService
        .getTrackingStream(widget.order.id)
        .listen(
          (tracking) {
            if (tracking != null) {
              setState(() {
                _currentTracking = tracking;
                _isLoading = false;
              });
              _updateMapWithTracking(tracking);
            } else {
              // No tracking data found, create initial tracking
              _createInitialTracking();
            }
          },
          onError: (error) {
            setState(() {
              _error = 'Error loading tracking data: $error';
              _isLoading = false;
            });
          },
        );
  }

  Future<void> _createInitialTracking() async {
    if (widget.order.deliveryLatitude == null ||
        widget.order.deliveryLongitude == null) {
      return;
    }

    final destinationLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    await _trackingService.createDeliveryTracking(
      orderId: widget.order.id,
      deliveryAgentId: 'demo_agent_001',
      deliveryAgentName: widget.order.deliveryAgentName ?? 'Delivery Agent',
      deliveryAgentPhone: widget.order.deliveryAgentPhone ?? '+91 9876543210',
      destinationLocation: destinationLocation,
      deliveryAgentAvatar: 'https://via.placeholder.com/100',
      vehicleType: 'Bike',
      vehicleNumber: 'UP 80 AB 1234',
    );
  }

  void _updateMapWithTracking(DeliveryTrackingModel tracking) {
    _updateMarkers(tracking);
    _updatePolylines(tracking);
    _animateCamera(tracking);
    _routeController.forward();
  }

  void _updateMarkers(DeliveryTrackingModel tracking) {
    final newMarkers = <Marker>{};

    // Store marker
    newMarkers.add(
      Marker(
        markerId: const MarkerId('store'),
        position: tracking.storeLocation,
        icon:
            _storeIcon ??
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        infoWindow: const InfoWindow(
          title: 'Smart Kirana Store',
          snippet: 'Center Point, Aligarh',
        ),
      ),
    );

    // Delivery agent marker with animation
    newMarkers.add(
      Marker(
        markerId: const MarkerId('delivery_agent'),
        position: tracking.currentLocation,
        icon:
            _deliveryAgentIcon ??
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: InfoWindow(
          title: tracking.deliveryAgentName,
          snippet: '${tracking.vehicleType} • ${tracking.vehicleNumber}',
        ),
        rotation: _calculateBearing(
          tracking.currentLocation,
          tracking.destinationLocation,
        ),
      ),
    );

    // Customer marker
    newMarkers.add(
      Marker(
        markerId: const MarkerId('customer'),
        position: tracking.destinationLocation,
        icon:
            _customerIcon ??
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        infoWindow: InfoWindow(
          title: 'Delivery Location',
          snippet: widget.order.userName,
        ),
      ),
    );

    setState(() {
      _markers = newMarkers;
    });
  }

  void _updatePolylines(DeliveryTrackingModel tracking) {
    final newPolylines = <Polyline>{};

    if (tracking.routePoints.isNotEmpty) {
      // Main route polyline
      newPolylines.add(
        Polyline(
          polylineId: const PolylineId('route'),
          points: tracking.routePoints,
          color: AppColors.primary,
          width: 4,
          patterns: [PatternItem.dash(20), PatternItem.gap(10)],
        ),
      );

      // Completed route (from store to current location)
      final completedRoute = _getCompletedRoute(tracking);
      if (completedRoute.isNotEmpty) {
        newPolylines.add(
          Polyline(
            polylineId: const PolylineId('completed_route'),
            points: completedRoute,
            color: AppColors.success,
            width: 6,
          ),
        );
      }
    }

    setState(() {
      _polylines = newPolylines;
    });
  }

  List<LatLng> _getCompletedRoute(DeliveryTrackingModel tracking) {
    // Find the closest point on route to current location
    if (tracking.routePoints.isEmpty) return [];

    int closestIndex = 0;
    double minDistance = double.infinity;

    for (int i = 0; i < tracking.routePoints.length; i++) {
      final distance = _calculateDistance(
        tracking.currentLocation,
        tracking.routePoints[i],
      );
      if (distance < minDistance) {
        minDistance = distance;
        closestIndex = i;
      }
    }

    return tracking.routePoints.sublist(0, closestIndex + 1);
  }

  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // Earth's radius in meters
    final double lat1Rad = point1.latitude * (3.14159 / 180);
    final double lat2Rad = point2.latitude * (3.14159 / 180);
    final double deltaLatRad =
        (point2.latitude - point1.latitude) * (3.14159 / 180);
    final double deltaLngRad =
        (point2.longitude - point1.longitude) * (3.14159 / 180);

    final double a =
        sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) *
            cos(lat2Rad) *
            sin(deltaLngRad / 2) *
            sin(deltaLngRad / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  double _calculateBearing(LatLng start, LatLng end) {
    final double lat1Rad = start.latitude * (3.14159 / 180);
    final double lat2Rad = end.latitude * (3.14159 / 180);
    final double deltaLngRad =
        (end.longitude - start.longitude) * (3.14159 / 180);

    final double y = sin(deltaLngRad) * cos(lat2Rad);
    final double x =
        cos(lat1Rad) * sin(lat2Rad) -
        sin(lat1Rad) * cos(lat2Rad) * cos(deltaLngRad);

    return (atan2(y, x) * (180 / 3.14159) + 360) % 360;
  }

  void _animateCamera(DeliveryTrackingModel tracking) {
    if (_mapController == null) return;

    final bounds = _calculateBounds([
      tracking.storeLocation,
      tracking.currentLocation,
      tracking.destinationLocation,
    ]);

    _mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100.0));
  }

  LatLngBounds _calculateBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (final point in points) {
      minLat = minLat < point.latitude ? minLat : point.latitude;
      maxLat = maxLat > point.latitude ? maxLat : point.latitude;
      minLng = minLng < point.longitude ? minLng : point.longitude;
      maxLng = maxLng > point.longitude ? maxLng : point.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        child: _buildMapContent(),
      ),
    );
  }

  Widget _buildMapContent() {
    if (_isLoading) {
      return Container(
        color: AppColors.background,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Container(
        color: AppColors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: AppColors.error),
              const SizedBox(height: AppPadding.medium),
              Text(
                _error!,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: AppColors.error),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return GoogleMap(
          initialCameraPosition: const CameraPosition(
            target: LatLng(27.8974, 78.0880), // Aligarh
            zoom: 12.0,
          ),
          markers: _markers,
          polylines: _polylines,
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
            if (_currentTracking != null) {
              _animateCamera(_currentTracking!);
            }
          },
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: !widget.showFullScreen,
          mapToolbarEnabled: false,
          compassEnabled: true,
          trafficEnabled: true,
          buildingsEnabled: true,
          indoorViewEnabled: false,
          mapType: MapType.normal,
          style: _getMapStyle(),
        )
        .animate()
        .fadeIn(duration: 500.ms)
        .scale(
          begin: const Offset(0.8, 0.8),
          end: const Offset(1.0, 1.0),
          duration: 500.ms,
          curve: Curves.easeOutBack,
        );
  }

  String? _getMapStyle() {
    // Custom map style for better appearance
    return '''
    [
      {
        "featureType": "poi",
        "elementType": "labels",
        "stylers": [{"visibility": "off"}]
      },
      {
        "featureType": "transit",
        "elementType": "labels",
        "stylers": [{"visibility": "off"}]
      }
    ]
    ''';
  }
}
