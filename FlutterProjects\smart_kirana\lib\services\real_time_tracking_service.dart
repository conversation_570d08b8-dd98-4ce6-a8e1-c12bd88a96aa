import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/delivery_tracking_model.dart';

class RealTimeTrackingService {
  static final RealTimeTrackingService _instance = RealTimeTrackingService._internal();
  factory RealTimeTrackingService() => _instance;
  RealTimeTrackingService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  String? _apiKey;
  bool _isInitialized = false;

  // Initialize the service
  Future<bool> initialize() async {
    try {
      if (!dotenv.isInitialized) {
        await dotenv.load(fileName: ".env");
      }
      _apiKey = dotenv.env['GOOGLE_MAPS_API_KEY'];
      
      if (_apiKey == null || _apiKey!.isEmpty) {
        _apiKey = 'AIzaSyCfvatSwmkaCzwDML5YkaxEO1uMMGxJwNE';
      }
      
      _isInitialized = true;
      return true;
    } catch (e) {
      debugPrint('Failed to initialize RealTimeTrackingService: $e');
      return false;
    }
  }

  // Create a new delivery tracking record
  Future<String?> createDeliveryTracking({
    required String orderId,
    required String deliveryAgentId,
    required String deliveryAgentName,
    required String deliveryAgentPhone,
    required LatLng destinationLocation,
    String deliveryAgentAvatar = '',
    String vehicleType = 'Bike',
    String vehicleNumber = '',
  }) async {
    try {
      const storeLocation = LatLng(27.8974, 78.0880); // Center Point, Aligarh
      
      final trackingRef = _firestore.collection('delivery_tracking').doc();
      
      final tracking = DeliveryTrackingModel(
        id: trackingRef.id,
        orderId: orderId,
        deliveryAgentId: deliveryAgentId,
        deliveryAgentName: deliveryAgentName,
        deliveryAgentPhone: deliveryAgentPhone,
        deliveryAgentAvatar: deliveryAgentAvatar,
        status: DeliveryStatus.assigned,
        currentLocation: storeLocation,
        storeLocation: storeLocation,
        destinationLocation: destinationLocation,
        routePoints: [],
        distanceRemaining: 0.0,
        estimatedTimeRemaining: 0,
        lastUpdated: DateTime.now(),
        events: [
          TrackingEvent(
            type: TrackingEventType.orderAssigned,
            message: 'Order assigned to $deliveryAgentName',
            timestamp: DateTime.now(),
            location: storeLocation,
          ),
        ],
        vehicleType: vehicleType,
        vehicleNumber: vehicleNumber,
      );

      await trackingRef.set(tracking.toMap());
      
      // Calculate initial route and distance
      await _updateRouteAndETA(trackingRef.id, storeLocation, destinationLocation);
      
      return trackingRef.id;
    } catch (e) {
      debugPrint('Error creating delivery tracking: $e');
      return null;
    }
  }

  // Get real-time tracking stream for an order
  Stream<DeliveryTrackingModel?> getTrackingStream(String orderId) {
    return _firestore
        .collection('delivery_tracking')
        .where('orderId', isEqualTo: orderId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.docs.isEmpty) return null;
      return DeliveryTrackingModel.fromMap(
        snapshot.docs.first.data(),
        snapshot.docs.first.id,
      );
    });
  }

  // Update delivery agent location
  Future<bool> updateDeliveryAgentLocation({
    required String trackingId,
    required LatLng newLocation,
    DeliveryStatus? newStatus,
    String? eventMessage,
  }) async {
    try {
      final trackingRef = _firestore.collection('delivery_tracking').doc(trackingId);
      final trackingDoc = await trackingRef.get();
      
      if (!trackingDoc.exists) return false;
      
      final tracking = DeliveryTrackingModel.fromMap(trackingDoc.data()!, trackingDoc.id);
      
      // Calculate distance and ETA
      final distance = _calculateDistance(newLocation, tracking.destinationLocation);
      final eta = _calculateETA(distance, tracking.averageSpeed);
      
      final updateData = <String, dynamic>{
        'currentLocation': {
          'latitude': newLocation.latitude,
          'longitude': newLocation.longitude,
        },
        'distanceRemaining': distance,
        'estimatedTimeRemaining': eta,
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
      };

      if (newStatus != null) {
        updateData['status'] = newStatus.name;
      }

      // Add tracking event if provided
      if (eventMessage != null) {
        final newEvent = TrackingEvent(
          type: _getEventTypeFromStatus(newStatus ?? tracking.status),
          message: eventMessage,
          timestamp: DateTime.now(),
          location: newLocation,
        );
        
        final updatedEvents = [...tracking.events, newEvent];
        updateData['events'] = updatedEvents.map((e) => e.toMap()).toList();
      }

      await trackingRef.update(updateData);
      
      // Update route if significant location change
      if (_calculateDistance(newLocation, tracking.currentLocation) > 0.1) {
        await _updateRouteAndETA(trackingId, newLocation, tracking.destinationLocation);
      }
      
      return true;
    } catch (e) {
      debugPrint('Error updating delivery agent location: $e');
      return false;
    }
  }

  // Update route and ETA
  Future<void> _updateRouteAndETA(String trackingId, LatLng origin, LatLng destination) async {
    try {
      if (!_isInitialized || _apiKey == null) return;

      final String url = 'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&mode=driving'
          '&key=$_apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final leg = route['legs'][0];
          
          // Decode polyline points
          final polylinePoints = route['overview_polyline']['points'];
          final routePoints = _decodePolyline(polylinePoints);
          
          final distance = leg['distance']['value'] / 1000.0; // Convert to km
          final duration = leg['duration']['value'] / 60; // Convert to minutes
          
          await _firestore.collection('delivery_tracking').doc(trackingId).update({
            'routePoints': routePoints.map((point) => {
              'latitude': point.latitude,
              'longitude': point.longitude,
            }).toList(),
            'distanceRemaining': distance,
            'estimatedTimeRemaining': duration,
          });
        }
      }
    } catch (e) {
      debugPrint('Error updating route and ETA: $e');
    }
  }

  // Simulate delivery agent movement for demo
  Stream<LatLng> simulateDeliveryMovement({
    required String trackingId,
    required LatLng startLocation,
    required LatLng endLocation,
    Duration interval = const Duration(seconds: 5),
  }) async* {
    const int totalSteps = 50;
    final latStep = (endLocation.latitude - startLocation.latitude) / totalSteps;
    final lngStep = (endLocation.longitude - startLocation.longitude) / totalSteps;

    for (int i = 0; i <= totalSteps; i++) {
      final currentLocation = LatLng(
        startLocation.latitude + (latStep * i),
        startLocation.longitude + (lngStep * i),
      );

      yield currentLocation;

      // Update Firebase with new location
      await updateDeliveryAgentLocation(
        trackingId: trackingId,
        newLocation: currentLocation,
        eventMessage: i == 0 ? 'Started delivery' : 
                    i == totalSteps ~/ 4 ? 'Picked up from store' :
                    i == totalSteps ~/ 2 ? 'Halfway to destination' :
                    i == (totalSteps * 0.8).round() ? 'Near destination' :
                    i == totalSteps ? 'Delivered successfully' : null,
        newStatus: i == 0 ? DeliveryStatus.pickedUp :
                  i == totalSteps ~/ 4 ? DeliveryStatus.inTransit :
                  i == (totalSteps * 0.8).round() ? DeliveryStatus.nearDestination :
                  i == totalSteps ? DeliveryStatus.delivered : null,
      );

      if (i < totalSteps) {
        await Future.delayed(interval);
      }
    }
  }

  // Helper methods
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double lat1Rad = point1.latitude * pi / 180;
    final double lat2Rad = point2.latitude * pi / 180;
    final double deltaLatRad = (point2.latitude - point1.latitude) * pi / 180;
    final double deltaLngRad = (point2.longitude - point1.longitude) * pi / 180;

    final double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  int _calculateETA(double distanceKm, double averageSpeedKmh) {
    return (distanceKm / averageSpeedKmh * 60).round(); // Convert to minutes
  }

  TrackingEventType _getEventTypeFromStatus(DeliveryStatus status) {
    switch (status) {
      case DeliveryStatus.assigned:
        return TrackingEventType.orderAssigned;
      case DeliveryStatus.pickedUp:
        return TrackingEventType.pickedUpFromStore;
      case DeliveryStatus.inTransit:
        return TrackingEventType.inTransit;
      case DeliveryStatus.nearDestination:
        return TrackingEventType.nearDestination;
      case DeliveryStatus.delivered:
        return TrackingEventType.delivered;
      case DeliveryStatus.failed:
        return TrackingEventType.failed;
    }
  }

  List<LatLng> _decodePolyline(String polyline) {
    List<LatLng> points = [];
    int index = 0, len = polyline.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = polyline.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = polyline.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }

    return points;
  }

  // Complete delivery
  Future<bool> completeDelivery(String trackingId) async {
    try {
      await _firestore.collection('delivery_tracking').doc(trackingId).update({
        'status': DeliveryStatus.delivered.name,
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
      });
      return true;
    } catch (e) {
      debugPrint('Error completing delivery: $e');
      return false;
    }
  }

  // Cancel delivery
  Future<bool> cancelDelivery(String trackingId, String reason) async {
    try {
      final trackingRef = _firestore.collection('delivery_tracking').doc(trackingId);
      final trackingDoc = await trackingRef.get();
      
      if (!trackingDoc.exists) return false;
      
      final tracking = DeliveryTrackingModel.fromMap(trackingDoc.data()!, trackingDoc.id);
      
      final cancelEvent = TrackingEvent(
        type: TrackingEventType.failed,
        message: 'Delivery cancelled: $reason',
        timestamp: DateTime.now(),
        location: tracking.currentLocation,
      );
      
      final updatedEvents = [...tracking.events, cancelEvent];
      
      await trackingRef.update({
        'status': DeliveryStatus.failed.name,
        'events': updatedEvents.map((e) => e.toMap()).toList(),
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
      });
      
      return true;
    } catch (e) {
      debugPrint('Error cancelling delivery: $e');
      return false;
    }
  }
}
