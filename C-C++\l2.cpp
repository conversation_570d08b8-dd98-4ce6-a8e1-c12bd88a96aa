#include<iostream>
using namespace std;

struct node
{
    int data;
    struct node *next;
};

void linkedlist(struct node *ptr)
{
    struct node *pointer = ptr;
    do
    {
        cout<<"Element : "<<ptr -> data<<endl;
        ptr = ptr -> next;
    }while (pointer != ptr);
}

int main()
{
    struct node *head;
    struct node *second;

    head = (struct node *)malloc(sizeof(struct node));
    second = (struct node *)malloc(sizeof(struct node));

    // Linked head to second
    head -> data = 7;
    head -> next = second;

    // Linked second to NULL pointer
    second -> data = 25;
    second -> next = head;

    linkedlist(head);

    return 0;
}