// ignore_for_file: prefer_const_constructors
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: "Day2 App", // Change this to your app's name
      theme: ThemeData.dark(),
      debugShowCheckedModeBanner: false,
      home: const MyHomePage("Day2 App"), // Change this to your home page widget
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage(this.title, {super.key});

  final String title;
  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text(widget.title));
  }
}

// class MyApp extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       theme: ThemeData.dark(),
//       title: 'My First App',
//       debugShowCheckedModeBanner: false,
//       home: Scaffold(
//         appBar: AppBar(title: Text('Home Page'),
//         floatingActionButton: FloatingActionButton(
//           child: Icon(Icons.add),
//           onPressed: () {},
//         )),
//         body: Row(
//           children: [
//             Text('Assalamu Alaikum, '),
//             Text('Yousuf!')
//           ]
//         ),
//       ),
//     );
//   }
// }
