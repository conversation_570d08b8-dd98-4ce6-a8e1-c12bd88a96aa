import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

enum DeliveryStatus {
  assigned,
  pickedUp,
  inTransit,
  nearDestination,
  delivered,
  failed,
}

enum TrackingEventType {
  orderAssigned,
  agentStarted,
  pickedUpFromStore,
  inTransit,
  nearDestination,
  delivered,
  delayed,
  failed,
}

class DeliveryTrackingModel {
  final String id;
  final String orderId;
  final String deliveryAgentId;
  final String deliveryAgentName;
  final String deliveryAgentPhone;
  final String deliveryAgentAvatar;
  final DeliveryStatus status;
  final LatLng currentLocation;
  final LatLng storeLocation;
  final LatLng destinationLocation;
  final List<LatLng> routePoints;
  final double distanceRemaining; // in kilometers
  final int estimatedTimeRemaining; // in minutes
  final DateTime lastUpdated;
  final List<TrackingEvent> events;
  final double averageSpeed; // km/h
  final String vehicleType;
  final String vehicleNumber;

  const DeliveryTrackingModel({
    required this.id,
    required this.orderId,
    required this.deliveryAgentId,
    required this.deliveryAgentName,
    required this.deliveryAgentPhone,
    required this.deliveryAgentAvatar,
    required this.status,
    required this.currentLocation,
    required this.storeLocation,
    required this.destinationLocation,
    required this.routePoints,
    required this.distanceRemaining,
    required this.estimatedTimeRemaining,
    required this.lastUpdated,
    required this.events,
    this.averageSpeed = 25.0,
    this.vehicleType = 'Bike',
    this.vehicleNumber = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'orderId': orderId,
      'deliveryAgentId': deliveryAgentId,
      'deliveryAgentName': deliveryAgentName,
      'deliveryAgentPhone': deliveryAgentPhone,
      'deliveryAgentAvatar': deliveryAgentAvatar,
      'status': status.name,
      'currentLocation': {
        'latitude': currentLocation.latitude,
        'longitude': currentLocation.longitude,
      },
      'storeLocation': {
        'latitude': storeLocation.latitude,
        'longitude': storeLocation.longitude,
      },
      'destinationLocation': {
        'latitude': destinationLocation.latitude,
        'longitude': destinationLocation.longitude,
      },
      'routePoints': routePoints.map((point) => {
        'latitude': point.latitude,
        'longitude': point.longitude,
      }).toList(),
      'distanceRemaining': distanceRemaining,
      'estimatedTimeRemaining': estimatedTimeRemaining,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'events': events.map((event) => event.toMap()).toList(),
      'averageSpeed': averageSpeed,
      'vehicleType': vehicleType,
      'vehicleNumber': vehicleNumber,
    };
  }

  factory DeliveryTrackingModel.fromMap(Map<String, dynamic> map, String docId) {
    return DeliveryTrackingModel(
      id: docId,
      orderId: map['orderId'] ?? '',
      deliveryAgentId: map['deliveryAgentId'] ?? '',
      deliveryAgentName: map['deliveryAgentName'] ?? '',
      deliveryAgentPhone: map['deliveryAgentPhone'] ?? '',
      deliveryAgentAvatar: map['deliveryAgentAvatar'] ?? '',
      status: DeliveryStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => DeliveryStatus.assigned,
      ),
      currentLocation: LatLng(
        map['currentLocation']['latitude'] ?? 0.0,
        map['currentLocation']['longitude'] ?? 0.0,
      ),
      storeLocation: LatLng(
        map['storeLocation']['latitude'] ?? 27.8974,
        map['storeLocation']['longitude'] ?? 78.0880,
      ),
      destinationLocation: LatLng(
        map['destinationLocation']['latitude'] ?? 0.0,
        map['destinationLocation']['longitude'] ?? 0.0,
      ),
      routePoints: (map['routePoints'] as List? ?? [])
          .map((point) => LatLng(
                point['latitude'] ?? 0.0,
                point['longitude'] ?? 0.0,
              ))
          .toList(),
      distanceRemaining: (map['distanceRemaining'] ?? 0.0).toDouble(),
      estimatedTimeRemaining: map['estimatedTimeRemaining'] ?? 0,
      lastUpdated: (map['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
      events: (map['events'] as List? ?? [])
          .map((event) => TrackingEvent.fromMap(event))
          .toList(),
      averageSpeed: (map['averageSpeed'] ?? 25.0).toDouble(),
      vehicleType: map['vehicleType'] ?? 'Bike',
      vehicleNumber: map['vehicleNumber'] ?? '',
    );
  }

  DeliveryTrackingModel copyWith({
    String? orderId,
    String? deliveryAgentId,
    String? deliveryAgentName,
    String? deliveryAgentPhone,
    String? deliveryAgentAvatar,
    DeliveryStatus? status,
    LatLng? currentLocation,
    LatLng? storeLocation,
    LatLng? destinationLocation,
    List<LatLng>? routePoints,
    double? distanceRemaining,
    int? estimatedTimeRemaining,
    DateTime? lastUpdated,
    List<TrackingEvent>? events,
    double? averageSpeed,
    String? vehicleType,
    String? vehicleNumber,
  }) {
    return DeliveryTrackingModel(
      id: id,
      orderId: orderId ?? this.orderId,
      deliveryAgentId: deliveryAgentId ?? this.deliveryAgentId,
      deliveryAgentName: deliveryAgentName ?? this.deliveryAgentName,
      deliveryAgentPhone: deliveryAgentPhone ?? this.deliveryAgentPhone,
      deliveryAgentAvatar: deliveryAgentAvatar ?? this.deliveryAgentAvatar,
      status: status ?? this.status,
      currentLocation: currentLocation ?? this.currentLocation,
      storeLocation: storeLocation ?? this.storeLocation,
      destinationLocation: destinationLocation ?? this.destinationLocation,
      routePoints: routePoints ?? this.routePoints,
      distanceRemaining: distanceRemaining ?? this.distanceRemaining,
      estimatedTimeRemaining: estimatedTimeRemaining ?? this.estimatedTimeRemaining,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      events: events ?? this.events,
      averageSpeed: averageSpeed ?? this.averageSpeed,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicleNumber: vehicleNumber ?? this.vehicleNumber,
    );
  }
}

class TrackingEvent {
  final TrackingEventType type;
  final String message;
  final DateTime timestamp;
  final LatLng? location;

  const TrackingEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    this.location,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'message': message,
      'timestamp': Timestamp.fromDate(timestamp),
      'location': location != null
          ? {
              'latitude': location!.latitude,
              'longitude': location!.longitude,
            }
          : null,
    };
  }

  factory TrackingEvent.fromMap(Map<String, dynamic> map) {
    return TrackingEvent(
      type: TrackingEventType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TrackingEventType.orderAssigned,
      ),
      message: map['message'] ?? '',
      timestamp: (map['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      location: map['location'] != null
          ? LatLng(
              map['location']['latitude'] ?? 0.0,
              map['location']['longitude'] ?? 0.0,
            )
          : null,
    );
  }
}
