#include<ctime>
#include<iostream>
using namespace std;
int main()
{
	time_t current_time = time(0);
	tm* localTime = localtime(&current_time);
	if((localTime->tm_hour)>=0 && (localTime->tm_hour)<5) cout<<"Hello nocturnal"<<char(2)<<endl;
	else if((localTime->tm_hour)>=5 && (localTime->tm_hour)<12) cout<<"Good Morning "<<char(3)<<endl;
	else if((localTime->tm_hour)>=12 && (localTime->tm_hour)<16) cout<<"Good Afternoon "<<char(1)<<endl;
	else cout<<"Good Evening "<<char(4)<<endl;
	cout<<"\tIt\'s "<<localTime->tm_hour<<" hr : "<<localTime->tm_min<<" min : "<<localTime->tm_sec<<" sec";
//	cout<<"isdst : "<<localTime->tm_isdst<<endl;
//	cout<<"mday : "<<localTime->tm_mday<<endl;
//	cout<<"mon : "<<localTime->tm_mon<<endl;
//	cout<<"wday : "<<localTime->tm_wday<<endl;
//	cout<<"yday : "<<localTime->tm_yday<<endl;
//	cout<<"year : "<<localTime->tm_year-100<<endl;
	
	return 0;
}

