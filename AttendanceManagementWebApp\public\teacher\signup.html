<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../styles.css">
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
    <title>Teacher Signup - AMU Attendance Portal</title>
    <style>
        .error-message {
            color: red;
            font-size: 0.9em;
            margin-top: 5px;
            display: none; /* Initially hidden */
        }
    </style>
</head>
<body>
    <div class="footerFlex">
        <div class="container">
            <!-- Logo with name -->
            <a href="./teacherLogin.html" class="logo"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>
            <h2 class="login-heading">Teacher Signup</h2>

            <!-- Signup Form -->
            <form id="signupForm" class="login-form">
                <input type="text" id="name" name="name" placeholder="Full Name" required>
                <input type="email" id="email" name="email" placeholder="Email" required>
                <span id="emailError" class="error-message">Email is already registered.</span> <!-- Error message span for email -->

                <input type="text" id="department" name="department" placeholder="Department" required>
                <input type="password" id="password" name="password" placeholder="Password" required>
                <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm Password" required>
                <span id="passwordError" class="error-message">Passwords do not match.</span> <!-- Error message span for password -->
                
                <button type="submit" class="button">Register</button>
            </form>
        </div>

        <footer style="position: absolute; bottom: 0;">
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script>
        document.getElementById("signupForm").addEventListener("submit", async function (e) {
        e.preventDefault(); // Prevent form from submitting normally

        // Clear previous error messages
        document.getElementById("emailError").style.display = "none";
        document.getElementById("passwordError").style.display = "none";

        // Get form data
        const formData = {
            name: document.getElementById("name").value,
            email: document.getElementById("email").value,
            department: document.getElementById("department").value,
            password: document.getElementById("password").value,
            confirmPassword: document.getElementById("confirmPassword").value,
        };

        // Send data to backend using fetch API
        try {
            const response = await fetch("/register", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(formData),
            });

            const result = await response.json();

            // Handle errors if they exist
            if (!response.ok) {
                if (result.message === "Email is already registered.") {
                    document.getElementById("emailError").style.display = "block";
                    document.getElementById("emailError").textContent = result.message;
                }
                if (result.message === "Password must be at least 6 characters long.") {
                    document.getElementById("passwordError").style.display = "block";
                    document.getElementById("passwordError").textContent = result.message;
                }
                if (result.message === "Passwords do not match.") {
                    document.getElementById("passwordError").style.display = "block";
                    document.getElementById("passwordError").textContent = result.message;
                }
            } else {
                // Registration successful - redirect to login or success page
                alert("Registration successful!");
                window.location.href = "../teacher/teacherLogin.html";
            }
        } catch (error) {
            console.error("Error:", error);
        }
    });
    </script>
</body>
</html>
