<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Link CSS  -->
    <link rel="stylesheet" href="../styles.css">

    <!-- Link Favicon  -->
    <link rel="shortcut icon" href="../assets/favicon.svg" type="image/x-icon">
    
    <title>Student Profile - AMU Attendance Portal</title>
</head>
<body>
    <div class="Dashboard-container">
        <!-- Navigation Bar -->
        <nav class="navbar">
            <div class="navbar-container">
                <a href="./parentDashboard.html"><img src="../assets/logoWithName.png" alt="AMU Logo with name" class="logo"></a>
                <!-- Dropdown Menu with Student Image -->
                <div class="dropdown">
                    <button class="dropbtn">
                        <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image"> 
                        Student Name &#9662;
                    </button>
                    <div class="dropdown-content">
                        <a href="./profile.html"><i class="fa-regular fa-address-card fa-fade"></i> Profile</a>
                        <a href="#" onclick="logoutParent()"><i class="fa-solid fa-arrow-right-from-bracket fa-fade"></i> <i class="fa-solid fa-person-walking fa-fade"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Container for Profile Card -->
        <div class="profile-container">
            <div class="profile-card">
                <h2 class="profile-heading">Profile</h2>
                <!-- Student Details -->
                <div class="profile-details">
                    <p><strong>Student Name:</strong> <span id="student-name">Loading...</span></p>
                    <p><strong>Enrollment Number:</strong> <span id="enrollment-number">Loading...</span></p>
                    <p><strong>Faculty Number:</strong> <span id="faculty-number">Loading...</span></p>
                    <p><strong>Course:</strong> <span id="course">Loading...</span></p>
                    <p><strong>Semester:</strong> <span id="semester">Loading...</span></p>
                </div>
            </div>
        </div>

        <footer>
            <p><i class="fa-solid fa-code fa-fade"></i> Developed by <a href="https://github.com/mohdyusuf2312" target="_blank"><strong>Mohd Yusuf</strong></a></p>
        </footer>
    </div>

    <script src="https://kit.fontawesome.com/740d826d31.js" crossorigin="anonymous"></script>
    <script>
        // Logout function
        async function logoutParent() {
            try {
                const response = await fetch("/studentLogout", {
                    method: "POST",
                    // credentials: "same-origin", // Ensures cookies are included
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({ logout: true })
                });
                if (response.ok) {
                    window.location.href = "./parentLogin.html"; // Redirect to the home page
                } else {
                    console.error('Logout failed:', response.statusText);
                }
            } catch (error) {
                console.error('Error during logout:', error);
            }
        }

        // Fetch student profile data from the server
        document.addEventListener("DOMContentLoaded", async () => {
            try {
                const response = await fetch("/api/student-profile", {
                    method: "GET",
                    credentials: "same-origin", // Ensures cookies are included in the request
                    headers: { "Content-Type": "application/json" }
                });
    
                if (!response.ok) {
                    throw new Error("Failed to fetch student profile");
                }
    
                const data = await response.json();
    
                // Populate profile details with data from the server
                document.getElementById("student-name").textContent = data.name;
                document.getElementById("enrollment-number").textContent = data.enrollment_number;
                document.getElementById("faculty-number").textContent = data.faculty_number;
                document.getElementById("course").textContent = data.course;
                document.getElementById("semester").textContent = data.semester;
    
                // Update the teacher name in the navbar dropdown
                document.querySelector(".dropbtn").innerHTML = `
                    <img id="student-image" src="../assets/user'sPic.jpeg" alt="Student Image" class="student-image"> 
                    ${data.name} &#9662;
                `;
            } catch (error) {
                console.error("Error fetching profile data:", error);
                alert("Could not load profile information. Please try again.");
            }
        });
    </script>
</body>
</html>
