#include<iostream>
using namespace std;
int main()
{
    int a,b,c;
    cout<<"Enter three numbers "; //take input from user a,b and c
    cin>>a>>b>>c;
    if((a==b)&&(b==c)) //if all numbers are equal
    {
        cout<<"All numbers are equal";
    }
    else if(a==b||b==c||a==c)  // if Two of them are equal
    {
        if(a==b && a>c) //if first and second are equal and third one is smaller
        {
            cout<<a<<" and "<<b<<" both numbers are equal and greater than "<<c;
        }
        else if(a==b && a<c) // if first and second are equal and third one is larger
        {
            cout<<a<<" and "<<b<<" both numbers are equal but "<<c<<" is greater";
        }
        if(b==c && c>a)  //if second and third are equal and first one is smaller
        {
            cout<<b<<" and "<<c<<" both numbers are equal and greater than "<<a;
        }
        else if(b==c && c<a) //if second and third are equal and first one is larger
        {
            cout<<b<<" and "<<c<<" both numbers are equal but "<<a<<" is greater";
        }
        if(a==c && a>b)  // if first and third are equal and second one is smaller
        {
            cout<<a<<" and "<<c<<" both numbers are equal and greater than "<<b;
        }
        else if(a==c && a<b)  //if first and third are equal and second one is larger
        {
            cout<<a<<" and "<<b<<" both numbers are equal but "<<b<<" is greater";
        }
    }
    else //If all numbers are distint
    {
        if(a>b) // a>b
        {
            if(a>c) // a>b && a>c---> a is larger
            {
                cout<<a<<" is larger number";
            }
            else  // a>b but a<c ----> c is larger
            {
                cout<<c<<" is larger number";
            }
        }
        else // a<b
        {
            if(b>c)  // a<b && b>c----> b is larger
            {
                cout<<b<<" is larger number";
            }
            else  // a<b but b<c -----> c is larger
            {
                cout<<c<<" is larger number";
            }
        }
    }
    return 0;
}
