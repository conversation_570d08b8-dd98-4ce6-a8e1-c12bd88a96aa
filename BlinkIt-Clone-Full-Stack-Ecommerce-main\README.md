# BlinkIt-Clone-Full-Stack-Ecommerce
BlinkIt-Clone-Full-Stack-Ecommerce

![Alt text](Thumnails.png?raw=true "Title")

Build a complete e-commerce platform that looks like Blinkit using the MERN stack! In this project, we will create an online shopping site with key features, including product uploads, an admin panel, and management for categories and subcategories. We will use access and refresh tokens to establish secure user authentication. You will also learn how to set up password recovery, OTP-based email verification, and secure authentication processes. This project is a great way to improve your skills in MongoDB, Express, React, and Node.js.

# Demo 
![Alt text](Demo%201.gif?raw=true "demo1")
![Alt text](Demo%202.gif?raw=true "demo2")

# Assets File
Google Drive : https://drive.google.com/drive/folders/1llzO3ts3NJKrQ0A2XWZYaO-T0Qnyq6yO?usp=sharing

 <a href="https://www.linkedin.com/in/itsamitprajapati" target="_blank">
  <img src="https://img.shields.io/badge/LinkedIn-0077B5?style=for-the-badge&logo=linkedin&logoColor=white" alt="amit prajapati"/>
 </a>

 Youtube : Dynamic Coding with Amit

 
